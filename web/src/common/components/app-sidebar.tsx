import { cn } from '@/common/utils/tailwind';
import {
  ChevronLeft,
  ChevronRight,
  CircleSlash,
  ClipboardCheck,
  Home,
  MessageSquare,
  PlusCircle,
  SearchCode,
  Settings,
  Copy,
} from 'lucide-react';
import { useState } from 'react';
import { Link } from 'react-router-dom';
import { Button } from './ui/button';
import { useService } from '@byted-image/lv-bedrock/di';
import { IAccountService } from '../services/account/account-service.interface';
import { toast } from 'sonner';

export function AppSidebar() {
  const accountService = useService(IAccountService);
  const [collapsed, setCollapsed] = useState(false); // 默认收缩状态

  return (
    <div
      className={cn(
        'flex h-full flex-col border-r bg-muted/40 transition-all duration-300',
        collapsed ? 'w-[60px]' : 'w-[260px]',
      )}
    >
      <div className="flex h-14 items-center border-b px-4 justify-between">
        {!collapsed && (
          <Link to="/" className="flex items-center gap-2 font-semibold">
            <span className="text-xl">Codin</span>
          </Link>
        )}
        <Button variant="ghost" size="icon" onClick={() => setCollapsed(!collapsed)} className="ml-auto">
          {collapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
        </Button>
      </div>
      <div className="flex-1 overflow-auto py-2">
        <nav className="grid gap-1 px-2">
          <Link to="/">
            <Button variant="ghost" className={cn('justify-start', collapsed ? 'w-10 px-2' : 'w-full')} title="Home">
              <Home className={cn('h-4 w-4', collapsed ? '' : 'mr-2')} />
              {!collapsed && 'Home'}
            </Button>
          </Link>
          <Link to="/arch">
            <Button variant="ghost" className={cn('justify-start', collapsed ? 'w-10 px-2' : 'w-full')} title="Chats">
              <MessageSquare className={cn('h-4 w-4', collapsed ? '' : 'mr-2')} />
              {!collapsed && 'Chats'}
            </Button>
          </Link>
          <Link to="/history">
            <Button variant="ghost" className={cn('justify-start', collapsed ? 'w-10 px-2' : 'w-full')} title="Chats">
              <PlusCircle className={cn('h-4 w-4', collapsed ? '' : 'mr-2')} />
              {!collapsed && 'History'}
            </Button>
          </Link>
          <Link to="/graph">
            <Button variant="ghost" className={cn('justify-start', collapsed ? 'w-10 px-2' : 'w-full')} title="Graph">
              <CircleSlash className={cn('h-4 w-4', collapsed ? '' : 'mr-2')} />
              {!collapsed && 'Graph'}
            </Button>
          </Link>
          <Link to="/token">
            <Button variant="ghost" className={cn('justify-start', collapsed ? 'w-10 px-2' : 'w-full')} title="Token">
              <ClipboardCheck className={cn('h-4 w-4', collapsed ? '' : 'mr-2')} />
              {!collapsed && 'Token'}
            </Button>
          </Link>
          <Link to="/codesearch">
            <Button
              variant="ghost"
              className={cn('justify-start', collapsed ? 'w-10 px-2' : 'w-full')}
              title="CodeSearch"
            >
              <SearchCode className={cn('h-4 w-4', collapsed ? '' : 'mr-2')} />
              {!collapsed && 'Code Search'}
            </Button>
          </Link>
        </nav>
      </div>
      {!collapsed && (
        <div className="border-t p-4">
          <div className="flex items-center mb-2">
            <Settings className="mr-2 h-4 w-4" />
            <span className="font-medium">Dev 配置</span>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="flex items-center gap-2"
              onClick={() => {
                const userId = accountService.userInfo?.userId;
                if (userId) {
                  navigator.clipboard.writeText(userId);
                  toast.success('UID 已复制到剪贴板');
                }
              }}
            >
              <Copy className="h-4 w-4" />
              <span>复制 UID</span>
            </Button>
          </div>
        </div>
      )}
      {collapsed && (
        <div className="border-t p-2 flex justify-center">
          <Button variant="ghost" size="icon" className="h-8 w-8" title="设置">
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      )}
    </div>
  );
}
