namespace go codesearch

include "../base.thrift"
include "../agentserver.thrift"
include "../agentclient.thrift"
include "../thirdparty/flow/flow.devops.evaluation.object_callback.thrift"
include "summary.thrift"
typedef flow.devops.evaluation.object_callback.AgentExecuteResponse AgentExecuteResponse
typedef flow.devops.evaluation.object_callback.AgentExecuteRequest AgentExecuteRequest
typedef flow.devops.evaluation.object_callback.SearchObjectResponse SearchObjectResponse
typedef flow.devops.evaluation.object_callback.SearchObjectRequest SearchObjectRequest

struct GetSummaryRequest {
  1: required string Uid (go.tag = "json:\"uid\""),   // 用户 id
  2: required string Did (go.tag = "json:\"did\""),   // 用户设备 id
  3: required string RepoName (go.tag = "json:\"repo_name\""), // 仓库名
  4: required string Branch (go.tag = "json:\"branch\""), // 分支
  5: required string RepoPath (go.tag = "json:\"repo_path\""), // 仓库路径
  255: optional base.Base Base (go.tag = "json:\"base\""),
}

struct GetSummaryResponse {
  1: optional string Result (go.tag = "json:\"result\""),            // 结果
  2: required string Code (go.tag = "json:\"code\""),      // 接口回包

  255: base.BaseResp BaseResp (go.tag = "json:\"base_resp\""),
}

struct CodeModule {
  1: required list<CodeSnippet> Codes (go.tag = "json:\"codes\""), // 代码内容 / Code content
  2: required string Summary (go.tag = "json:\"summary\""), // 代码模块简要说明
}

/**
 * 代码片段结构体
 * Code snippet structure
 */
struct CodeSnippet {
  1: required string Content (go.tag = "json:\"content\""), // 代码内容 / Code content
  2: required string FilePath (go.tag = "json:\"file_path\""), // 文件路径 / File path
  3: required string Summary (go.tag = "json:\"summary\""), // 代码片段简要说明 / Brief description of the code snippet
}

struct CodeSearchRequest {
  1: required string Uid (go.tag = "json:\"uid\""),   // 用户 id
  2: required string Did (go.tag = "json:\"did\""),   // 用户设备 id
  3: required string RepoName (go.tag = "json:\"repo_name\""), // 仓库名
  4: required string Branch (go.tag = "json:\"branch\""), // 分支
  5: required string RepoPath (go.tag = "json:\"repo_path\""), // 仓库路径
  6: required list<string> PathList (go.tag = "json:\"path_list\""),  // 仓库路径列表
  7: required string Query (go.tag = "json:\"query\""), // 用户消息，需要搜索的自然语言描述
  8: optional string Mode (go.tag = "json:\"mode\""), // 搜索模式，fast，normal
  9: required list<string> QueryList (go.tag = "json:\"query_list\""), // 需要搜索的query列表，会并行搜索
  255: optional base.Base Base (go.tag = "json:\"base\""),
}

struct CodeSearchResponse {
  1: required list<CodeSnippet> Codes (go.tag = "json:\"codes\""), // 代码内容 / Code content
  2: required string Summary (go.tag = "json:\"summary\""), // 整体详细的总结报告 / Overall detailed summary report

  255: base.BaseResp BaseResp (go.tag = "json:\"base_resp\""),
}

struct SemanticCodeSearchRequest {
  1: required string Business (go.tag = "json:\"business\""), // 业务名,有些项目是大仓的，需要区分大仓里面的子业务
  2: required string Platform (go.tag = "json:\"platform\""), // 平台，如：web，server， lynx， android， ios
  3: required string Query (go.tag = "json:\"query\""), // 用户消息，需要搜索的自然语言描述

  255: optional base.Base Base (go.tag = "json:\"base\""),
}

struct SemanticCodeSearchResponse {
  1: required list<CodeSnippet> Codes (go.tag = "json:\"codes\""), // 代码内容 / Code content

  255: base.BaseResp BaseResp (go.tag = "json:\"base_resp\""),
}

struct CodeReviewRequest {
  1: optional string Id (go.tag = "json:\"id\""), // 会话id
  2: required string text (go.tag = "json:\"text\""),  // mr 链接
  3: optional base.ModelType ModelType (go.tag = "json:\"model_type\""),    // 模型类型，默认DeepSeek
  4: optional string SystemPrompt (go.tag = "json:\"system_prompt\""),    // 调试模式，端上携带systemprompt

  255: optional base.Base Base (go.tag = "json:\"base\""),
}

struct CodeReviewResponse {
  1: optional string Result (go.tag = "json:\"result\""),            // 评论
  2: required string Code (go.tag = "json:\"code\""),      // 接口回包

  255: base.BaseResp BaseResp (go.tag = "json:\"base_resp\""),
}

struct CreateSummaryResponse {
  1: required string Code (go.tag = "json:\"code\""),      // 接口回包

  255: base.BaseResp BaseResp (go.tag = "json:\"base_resp\""),
}

struct CreateSummaryRequest {
  1: required string RepoName (go.tag = "json:\"repo_name\""), // 仓库名
  2: required string RepoURL (go.tag = "json:\"repo_url\""), // 仓库地址
  3: required string Branch (go.tag = "json:\"branch\""), // 仓库分支

  255: optional base.Base Base (go.tag = "json:\"base\""),
}

struct DiffResult {
  1: required string Path (go.tag = "json:\"path\""),  // 文件/目录路径
  2: required string Type (go.tag = "json:\"type\""),   // 差异类型: "modify", "add", "delete", "type_mismatch", "path_mismatch"
  3: required string Hash (go.tag = "json:\"hash\""),   // 文件 hash
}

struct GetMerkleDiffRequest {
  1: required string MerkleTreeKey (go.tag = "json:\"merkle_tree_key\""),  // Gzip压缩后的仓库树数据
  2: required string Uid (go.tag = "json:\"uid\""), // 用户
  3: required string RepoName (go.tag = "json:\"repo_name\""),  // 仓库
  4: required string Branch (go.tag = "json:\"branch\""), // 分支
  5: required string RepoPath (go.tag = "json:\"repo_path\""), // 路径
  6: required string Did (go.tag = "json:\"did\""), // 设备id
}

struct GetMerkleDiffResponse {
  1: required list<DiffResult> diffs (go.tag = "json:\"diffs\""), // 差异结果
  3: required string OriginUserKnowledgeId (go.tag = "json:\"origin_user_knowledge_id\""), // 索引构建userKnowledgeId id  

  255: base.BaseResp BaseResp (go.tag = "json:\"base_resp\""),
}

struct UploadMerkleTreeRequest {
  1: required string MerkleTreeKey (go.tag = "json:\"merkle_tree_key\""),  // Gzip压缩后的仓库树数据
  2: required string OriginUserKnowledgeId (go.tag = "json:\"origin_user_knowledge_id\""), // 索引构建userKnowledgeId id
  
  3: required string Uid (go.tag = "json:\"uid\""), // 用户
  4: required string RepoName (go.tag = "json:\"repo_name\""),  // 仓库
  5: required string Branch (go.tag = "json:\"branch\""), // 分支
  6: required string RepoPath (go.tag = "json:\"repo_path\""), // 路径
  7: required string Did (go.tag = "json:\"did\""), // 设备id
  
  8: required string ChunkFileKey (go.tag = "json:\"chunk_file_key\""), // 新增 chunk 文件
  9: required string RelationsFileKey (go.tag = "json:\"relations_file_key\""), // 新增 relations 文件
  10: required list<string> DeleteFileIds (go.tag = "json:\"delete_file_ids\""), // 需要删除的文件hash
  11: required string RootMerkleId (go.tag = "json:\"root_merkle_id\""), // 根节点id
}

struct UploadMerkleTreeResponse {
  1: required string Id (go.tag = "json:\"id\""),      // 索引构建userKnowledgeId

  255: base.BaseResp BaseResp (go.tag = "json:\"base_resp\""),
}

struct QueryBuildRecordRequest {
  1: required string Uid (go.tag = "json:\"uid\""), // 用户
  2: required string RepoName (go.tag = "json:\"repo_name\""),  // 仓库
  3: required string Branch (go.tag = "json:\"branch\""), // 分支
  4: required string RepoPath (go.tag = "json:\"repo_path\""), // 路径
  5: required string Did (go.tag = "json:\"did\""), // 设备id
}

struct QueryBuildRecordResponse {
  1: required string RootMerkleId (go.tag = "json:\"root_merkle_id\""), // 根节点id
  // 构建状态 0: 构建中 1: 已构建 2: 构建失败, -1: 索引不存在
  2: required string BuildStatus (go.tag = "json:\"build_status\""), // 构建状态

  255: base.BaseResp BaseResp (go.tag = "json:\"base_resp\""),
}

struct BuildIndexRequest {
  1: required string RepoName (go.tag = "json:\"repoName\""), // 仓库名
  2: required string RepoURL (go.tag = "json:\"repoURL\""), // 仓库地址
  3: required string ProjectID (go.tag = "json:\"projectID\""), // 仓库 projectID
  4: required string Branch (go.tag = "json:\"branch\""), // 仓库分支
  5: required string Language (go.tag = "json:\"language\""), // 仓库语言
  255: optional base.Base Base (go.tag = "json:\"base\""),
}

struct BuildIndexResponse {

  255: base.BaseResp BaseResp (go.tag = "json:\"base_resp\""),
}

struct GetSummaryUpdateFilesRequest {
  1: required string MerkleTreeKey (go.tag = "json:\"merkle_tree_key\""),  // Gzip压缩后的仓库树数据
  2: required string Uid (go.tag = "json:\"uid\""), // 用户
  3: required string RepoName (go.tag = "json:\"repo_name\""),  // 仓库
  4: required string Branch (go.tag = "json:\"branch\""), // 分支
  5: required string RepoPath (go.tag = "json:\"repo_path\""), // 路径
  6: required string Did (go.tag = "json:\"did\""), // 设备id
}

struct GetSummaryUpdateFilesResponse {
  1: required summary.GroupedRelatedPathInfo GroupedRelatedPathInfo (go.tag = "json:\"grouped_related_path_info\""), // 差异结果
  2: required list<DiffResult> diffs (go.tag = "json:\"diffs\""), // 差异结果
  3: required string OriginUserKnowledgeId (go.tag = "json:\"origin_user_knowledge_id\""), // 索引构建userKnowledgeId id  
  4: optional string Result (go.tag = "json:\"result\""),            // 结果
  5: required string Code (go.tag = "json:\"code\""),      // 接口回包

  255: base.BaseResp BaseResp (go.tag = "json:\"base_resp\""),
}

struct UpdateSummaryRequest {
  1: required string MerkleTreeKey (go.tag = "json:\"merkle_tree_key\""),  // Gzip压缩后的仓库树数据
  2: required string Uid (go.tag = "json:\"uid\""), // 用户
  3: required string RepoName (go.tag = "json:\"repo_name\""),  // 仓库
  4: required string Branch (go.tag = "json:\"branch\""), // 分支
  5: required string RepoPath (go.tag = "json:\"repo_path\""), // 路径
  6: required string Did (go.tag = "json:\"did\""), // 设备id

  7: optional string GroupedRelatedFileInfoKey (go.tag = "json:\"grouped_related_file_info_key\""), // 差异结果

  8: required string OriginUserKnowledgeId (go.tag = "json:\"origin_user_knowledge_id\""), // 索引构建userKnowledgeId id
  9: required string RootMerkleId (go.tag = "json:\"root_merkle_id\""), // 根节点id
}

struct UpdateSummaryResponse {
  1: required string UserKnowledgeId (go.tag = "json:\"user_knowledge_id\""),      // 索引构建userKnowledgeId
  2: optional string Result (go.tag = "json:\"result\""),            // 结果
  3: required string Code (go.tag = "json:\"code\""),      // 接口回包

  255: base.BaseResp BaseResp (go.tag = "json:\"base_resp\""),
}

struct QuerySummaryBuildRecordRequest {
  1: required string Uid (go.tag = "json:\"uid\""), // 用户
  2: required string RepoName (go.tag = "json:\"repo_name\""),  // 仓库
  3: required string Branch (go.tag = "json:\"branch\""), // 分支
  4: required string RepoPath (go.tag = "json:\"repo_path\""), // 路径
  5: required string Did (go.tag = "json:\"did\""), // 设备id
}

struct QuerySummaryBuildRecordResponse {
  1: required string RootMerkleId (go.tag = "json:\"root_merkle_id\""), // 根节点id
  2: required string BuildStatus (go.tag = "json:\"build_status\""), // 构建状态
  3: optional string Result (go.tag = "json:\"result\""),            // 结果
  4: required string Code (go.tag = "json:\"code\""),      // 接口回包

  255: base.BaseResp BaseResp (go.tag = "json:\"base_resp\""),
}

struct QueryBuildRepoRequest {
  255: optional base.Base Base (go.tag = "json:\"base\""),
}

struct QueryBuildRepoItem {
  1: required string RepoName (go.tag = "json:\"repo_name\""),  // 仓库
  2: required string CreateAt (go.tag =  "json:\"gmt_create\""),  // 创建时间
  3: required string ModifyAt (go.tag =  "json:\"gmt_modify\""),  // 更新时间
  4: required string Branch (go.tag =  "json:\"branch\""),  // 分支
  5: required string Language (go.tag =  "json:\"language\""),  // language
  6: required string Uid (go.tag =  "json:\"uid\""),  // uid
  7: required string Status (go.tag =  "json:\"status\""),  // status
}

struct QueryBuildRepoResponse {
  1: required list<QueryBuildRepoItem> Items (go.tag = "json:\"items\""), // 查询结果
  255: base.BaseResp BaseResp (go.tag = "json:\"base_resp\""),
}



struct GetCodeSearchRequest {
  1: required agentserver.AgentInferenceParams Params (go.tag = "json:\"params\""),

  255: optional base.Base Base (go.tag = "json:\"base\""),
}


struct GetCodeSearchResponse {
  255: base.BaseResp BaseResp (go.tag = "json:\"base_resp\""),
}

// 重试生成计划请求
struct RetryCodeSearchRequest {
  1: required agentserver.AgentRetryParams Params (go.tag = "json:\"params\""),

  255: optional base.Base Base (go.tag = "json:\"base\""),
}

// 重试生成计划响应
struct RetryCodeSearchResponse {
  255: base.BaseResp BaseResp (go.tag = "json:\"base_resp\""),
}



/**
 * 代码搜索服务
 * Code search service
 */
service CodeSearchService {
    // 获取代码
    CodeSearchResponse GetCode (1: CodeSearchRequest request),
    // 获取语义搜索代码
    SemanticCodeSearchResponse GetSemanticCode (1: CodeSearchRequest request),
    // 生成 mr
    CodeReviewResponse GenMr (1: CodeReviewRequest request)
    // 创建摘要
    CreateSummaryResponse CreateSummary (1: CreateSummaryRequest request)
    // 获取摘要
    GetSummaryResponse GetSummary (1: GetSummaryRequest request)

    // 获取变更文件
    GetSummaryUpdateFilesResponse GetSummaryUpdateFiles (1: GetSummaryUpdateFilesRequest request)
    // 更新摘要
    UpdateSummaryResponse UpdateSummary (1: UpdateSummaryRequest request)
    // 查询摘要
    QuerySummaryBuildRecordResponse QuerySummaryBuildRecord (1: QuerySummaryBuildRecordRequest request)

    // 获取MerkleDiff
    GetMerkleDiffResponse GetMerkleDiff (1: GetMerkleDiffRequest request)
    // 上传MerkleTree
    UploadMerkleTreeResponse UploadMerkleTree (1: UploadMerkleTreeRequest request)
    // 查询索引构建结构
    QueryBuildRecordResponse QueryBuildRecord (1: QueryBuildRecordRequest request)
    
    // 构建 master 索引
    BuildIndexResponse BuildIndex (1: BuildIndexRequest request)
    // 查询构建记录
    QueryBuildRepoResponse QueryBuildRepo(1: QueryBuildRepoRequest request)
    // Agent Execute
    AgentExecuteResponse AgentExecute (1: AgentExecuteRequest request)
    // Search Object
    SearchObjectResponse SearchObject (1: SearchObjectRequest request)
    // socket生成接口
    GetCodeSearchResponse GetCodeSearch (1: GetCodeSearchRequest request),   
    // 重新生成socket接口
    RetryCodeSearchResponse RetryCodeSearch (1: RetryCodeSearchRequest request),   
}
