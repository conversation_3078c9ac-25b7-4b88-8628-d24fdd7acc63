package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"runtime/debug"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/ies/codin/application/codesearch/entity"
	codeEntity "code.byted.org/ies/codin/application/codesearch/entity/code"
	"code.byted.org/ies/codin/application/codesearch/eval"
	"code.byted.org/ies/codin/application/codesearch/logic"
	"code.byted.org/ies/codin/application/codesearch/logic/code"
	"code.byted.org/ies/codin/application/codesearch/logic/merklet"
	"code.byted.org/ies/codin/application/codesearch/logic/semantic"
	"code.byted.org/ies/codin/application/codesearch/logic/summary"
	summaryUpdate "code.byted.org/ies/codin/application/codesearch/logic/summary/update/summary_update"
	knowledgeUtils "code.byted.org/ies/codin/application/codesearch/repo/codebase/manager"
	"code.byted.org/ies/codin/application/codesearch/repo/tos"
	"code.byted.org/ies/codin/application/codesearch/repo/tos/config"
	workspaceEntity "code.byted.org/ies/codin/application/codesearch/repo/workspace/entity"
	"code.byted.org/ies/codin/common/group"
	"code.byted.org/ies/codin/common/login"
	"code.byted.org/ies/codin/common/rpcerr"
	"code.byted.org/ies/codin/common/utils"
	"code.byted.org/ies/codinmodel/kitex_gen/commonerrcode"
	"code.byted.org/overpass/capcut_devops_codesearch/kitex_gen/codesearch"
	"github.com/samber/lo"
)

type codesearchServiceImpl struct {
	app *logic.CodeSearch
}

// New 创建服务实例
func New(ctx context.Context) codesearch.CodeSearchService {
	return &codesearchServiceImpl{
		app: logic.NewCodeSearch(ctx),
	}
}

// GetCodeSearch socket生成
func (s *codesearchServiceImpl) GetCodeSearch(ctx context.Context,
	request *codesearch.GetCodeSearchRequest) (*codesearch.GetCodeSearchResponse, error) {
	uid := login.GetUid(ctx)
	if uid == "" {
		logs.CtxError(ctx, "codesearch agent failed, uid is empty")
		return nil, rpcerr.New(commonerrcode.ErrCode_Unauthorized, "uid is empty")
	}

	err := code.GetCodeSearch(ctx, s.app, request, uid)
	if err != nil {
		logs.CtxError(ctx, "codesearch agent GetCodeSearch failed, err: %v", err)
		return nil, err
	}

	return &codesearch.GetCodeSearchResponse{}, nil
}

// RetryCodeSearch socket重新生成
func (s *codesearchServiceImpl) RetryCodeSearch(ctx context.Context,
	request *codesearch.RetryCodeSearchRequest) (*codesearch.RetryCodeSearchResponse, error) {
	uid := login.GetUid(ctx)
	if uid == "" {
		logs.CtxError(ctx, "planning agent Planning failed, uid is empty")
		return nil, rpcerr.New(commonerrcode.ErrCode_Unauthorized, "uid is empty")
	}

	err := code.RetryCodeSearch(ctx, s.app, request, uid)
	if err != nil {
		logs.CtxError(ctx, "codesearch agent GenerateToSocket failed, err: %v", err)
		return nil, err
	}

	return &codesearch.RetryCodeSearchResponse{}, nil
}

func (s *codesearchServiceImpl) GetCode(ctx context.Context, request *codesearch.CodeSearchRequest) (*codesearch.CodeSearchResponse, error) {
	var resp *codesearch.CodeSearchResponse
	var err error
	var queries = request.QueryList
	logs.CtxInfo(ctx, "[GetCode] start request: %+v", request)

	if len(queries) > 0 {
		// #region queries 处理
		queries = append([]string{request.Query}, queries...) // 将 request.Query 放到 queries 第一个
		queries = utils.Uniq(queries)                         // 对 queries 去重
		if len(queries) > 4 {
			// 限制 queries 数量最大为 4
			queries = queries[:4]
		}
		// #endregion

		var mu sync.Mutex
		responses := make(map[string]*codesearch.CodeSearchResponse)
		handlers := make([]func() error, 0, len(queries))

		logs.CtxInfo(ctx, "[GetCode] parallel execution for %d queries", len(queries))

		for _, query := range queries {
			req := request
			req.Query = query

			handlers = append(handlers, func() error {
				r, e := s.getCode(ctx, req)
				if e != nil {
					logs.CtxError(ctx, "[GetCode] getCode error for query '%s': %+v", query, e)
					return nil
				}
				mu.Lock()
				responses[query] = r
				mu.Unlock()
				return nil
			})
		}

		if execErr := group.GoAndWait(handlers...); execErr != nil {
			logs.CtxWarn(ctx, "[GetCode] one or more getCode calls failed during execution: %+v", execErr)
		}

		// Combine results
		if len(responses) > 0 {
			resp = &codesearch.CodeSearchResponse{
				Summary: "",
				Codes:   []*codesearch.CodeSnippet{},
			}
			var summaryBuilder strings.Builder
			seenFilePaths := make(map[string]bool)
			for i, query := range queries {
				r, ok := responses[query]
				if !ok || r == nil {
					continue
				}
				if r.Summary != "" {
					summaryBuilder.WriteString(fmt.Sprintf("<query%d>%s</query%d>\n<answer%d>\n%s\n</answer%d>", i+1, query, i+1, i+1, r.Summary, i+1))
					summaryBuilder.WriteString("\n\n")
				}
				if r.Codes != nil {
					for _, code := range r.Codes {
						if _, seen := seenFilePaths[code.FilePath]; !seen {
							resp.Codes = append(resp.Codes, code)
							seenFilePaths[code.FilePath] = true
						}
					}
				}
			}
			resp.Summary = strings.TrimSpace(summaryBuilder.String())
		} else {
			err = fmt.Errorf("all parallel getCode calls failed or returned no results")
		}
	} else {
		resp, err = s.getCode(ctx, request)
	}

	return resp, err
}

func (s *codesearchServiceImpl) getCode(ctx context.Context, request *codesearch.CodeSearchRequest) (*codesearch.CodeSearchResponse, error) {
	// @todo 等 GetCode 好了之后这里改回去
	// if request.Mode != nil && *request.Mode == "fast" {
	// 	return code.FastGetCode(ctx, s.app, request)
	// }
	// return code.GetCode(ctx, s.app, request)
	return code.FastGetCode(ctx, s.app, request)
}

func (s *codesearchServiceImpl) GetSemanticCode(ctx context.Context, request *codesearch.CodeSearchRequest) (*codesearch.SemanticCodeSearchResponse, error) {
	return code.GetSemanticCode(ctx, s.app, request)
}

func (s *codesearchServiceImpl) GetSummary(ctx context.Context, request *codesearch.GetSummaryRequest) (*codesearch.GetSummaryResponse, error) {
	logs.CtxInfo(ctx, "[GetSummary] start request: %+v", request)
	return summary.GetSummary(ctx, s.app, request)
}

func (s *codesearchServiceImpl) GenMr(ctx context.Context, request *codesearch.CodeReviewRequest) (*codesearch.CodeReviewResponse, error) {
	// return controller.GenMr(ctx, s.app, request)
	return nil, nil
}

func (s *codesearchServiceImpl) CreateSummary(ctx context.Context, request *codesearch.CreateSummaryRequest) (*codesearch.CreateSummaryResponse, error) {
	logs.CtxInfo(ctx, "[CreateSummary] start request: %+v", request)
	_, err := summary.CreateSummary(ctx, s.app, request)
	if err != nil {
		logs.CtxError(ctx, "[CreateSummary] CreateSummary error: %v", err)
		return nil, err
	}
	return &codesearch.CreateSummaryResponse{
		Code: "200",
	}, nil
}

func (s *codesearchServiceImpl) AgentExecute(ctx context.Context, request *codesearch.AgentExecuteRequest) (*codesearch.AgentExecuteResponse, error) {
	if request == nil || request.Input == nil {
		return nil, fmt.Errorf("[AgentExecute]: invalid request, request or request.Input is nil")
	}

	if request.Input.Variables == nil {
		return nil, fmt.Errorf("[AgentExecute]: invalid request, Input.Variables is nil")
	}

	// 获取场景参数，如果不存在或无效则使用默认场景
	var scene entity.AgentScene
	sceneText, err := eval.CheckReqInputVariables(request.Input.Variables, "scene")
	if err != nil {
		return nil, fmt.Errorf("[AgentExecute]: invalid agentScene: %s", sceneText)
	}

	scene = lo.If(lo.IsEmpty(sceneText), entity.GetDefaultScene()).Else(entity.AgentScene(sceneText))
	logs.CtxInfo(ctx, "[AgentExecute] executing with scene: %s", scene)

	// 根据场景执行不同的处理逻辑
	switch scene {
	case entity.SemanticSearch:
		return eval.EvalManager.HandleSemanticSearch(ctx, request)
	case entity.AgentSceneCodeSearch:
		return eval.EvalManager.HandleCodeSearchScene(ctx, request, s.GetCode)
	}

	return nil, fmt.Errorf("[AgentExecute]: unsupported scene: %s", scene)
}

func (s *codesearchServiceImpl) SearchObject(ctx context.Context, request *codesearch.SearchObjectRequest) (*codesearch.SearchObjectResponse, error) {
	return nil, nil
}

// 比对前后端的 merkle tree 的差异
func (s *codesearchServiceImpl) GetMerkleDiff(ctx context.Context, request *codesearch.GetMerkleDiffRequest) (*codesearch.GetMerkleDiffResponse, error) {
	logs.CtxInfo(ctx, "[GetMerkletDiff] start request, repoName: %s, uid: %s, repoPath: %s, did: %s, branch: %s", request.RepoName, request.Uid, request.RepoPath, request.Did, request.Branch)
	userKnowledgeId := knowledgeUtils.GetDbUserKnowledgeId(request.RepoName, request.Uid, request.RepoPath, request.Did, request.Branch)
	knowledgebaseId := knowledgeUtils.GetDbKnowledgebaseId(request.RepoName)

	buildRecord, err := s.app.SemanticManager.FuzzyQueryBuildRecord(ctx, &codeEntity.FuzzyQueryBuildRecordRequest{
		UserKnowledgeId: userKnowledgeId,
		Branch:          request.Branch,
		KnowledgebaseId: knowledgebaseId,
	})
	if err != nil {
		logs.CtxInfo(ctx, "[GetMerkleDiff] repo not exist on remote config, skip")
		return &codesearch.GetMerkleDiffResponse{}, nil
	}

	tosManager := tos.NewTosManager()
	err = tosManager.InitTosClientIfNeeded(config.MerkleStorageConfig)
	if err != nil {
		logs.CtxError(ctx, "[GetMerkleDiff] init tos client error: %v", err)
		return nil, err
	}
	repoTreeBtyes, err := tosManager.DownloadFileFromTos(ctx, request.MerkleTreeKey)
	if err != nil {
		logs.CtxError(ctx, "[GetMerkleDiff] download repoTree error: %v", err)
		return nil, err
	}

	logs.CtxInfo(ctx, "userKnowledgeId: %s", userKnowledgeId)

	uncompressedData, err := merklet.GzipDecompress(repoTreeBtyes)
	if err != nil {
		logs.CtxError(ctx, "[GetMerkleDiff] failed to decompress repoTree: %v", err)
		return nil, err
	}

	// 2. 反序列化为树结构
	repoTree, err := merklet.DeserializeTree(uncompressedData)
	if err != nil {
		logs.CtxError(ctx, "[GetMerkleDiff] failed to deserialize tree: %v", err)
		return nil, err
	}
	if repoTree == nil {
		logs.CtxError(ctx, "[GetMerkleDiff] DeserializeTree error: %+v", err)
		return nil, err
	}

	if err != nil {
		logs.CtxError(ctx, "[GetMerkleDiff] QueryBuildRecord error: %+v", err)
		return nil, err
	}

	logs.CtxInfo(ctx, "buildRecord rootMerkleId: %s", buildRecord.RootMerkleId)

	merkleTree, err := s.app.SemanticManager.DownloadMerkleTree(ctx, buildRecord.RootMerkleId)
	if err != nil {
		logs.CtxError(ctx, "[GetMerkleDiff] DownloadMerkleTree error: %+v", err)
		return nil, err
	}

	// 2. 解压数据
	uncompressedData, err = merklet.GzipDecompress(merkleTree)
	if err != nil {
		logs.CtxError(ctx, "GzipUncompress error: %v", err)
		return nil, err
	}

	serverTree, err := merklet.DeserializeTree(uncompressedData)
	if err != nil {
		logs.CtxError(ctx, "DeserializeTree error: %v", err)
		return nil, err
	}

	logs.CtxInfo(ctx, "serverTree rootMerkleId: %v", serverTree.Hash)
	logs.CtxInfo(ctx, "repoTree rootMerkleId: %v", repoTree.Hash)

	diffs := merklet.DiffTrees(serverTree, repoTree)

	var diffResults []*codesearch.DiffResult_
	for _, diff := range diffs {
		diffResults = append(diffResults, &codesearch.DiffResult_{
			Path: diff.Path,
			Type: string(diff.Type),
			Hash: diff.Hash,
		})
	}

	return &codesearch.GetMerkleDiffResponse{
		Diffs:                 diffResults,
		OriginUserKnowledgeId: buildRecord.UserKnowledgeId,
	}, nil
}

// 应用前端上行的 merkle tree 进行构建索引
func (s *codesearchServiceImpl) UploadMerkleTree(ctx context.Context, request *codesearch.UploadMerkleTreeRequest) (*codesearch.UploadMerkleTreeResponse, error) {
	uid := request.Uid
	repoName := request.RepoName
	branch := request.Branch
	repoPath := request.RepoPath
	did := request.Did
	deleteFileIds := request.DeleteFileIds
	rootMerkleId := request.RootMerkleId
	originUserKnowledgeId := request.OriginUserKnowledgeId
	logs.CtxInfo(ctx, "[UploadMerkleTree] start request, uid: %v, repoName: %v, branch: %v, repoPath: %v, did: %v, originUserKnowledgeId: %v, rootMerkleId: %v, deleteFileIds: %v, ", uid, repoName, branch, repoPath, did, originUserKnowledgeId, rootMerkleId, deleteFileIds)

	if originUserKnowledgeId == "" {
		logs.CtxError(ctx, "empty originUserKnowledgeId")
		return &codesearch.UploadMerkleTreeResponse{}, nil
	}

	// download files from tos
	tosManager := tos.NewTosManager()
	err := tosManager.InitTosClientIfNeeded(config.MerkleStorageConfig)
	if err != nil {
		return nil, err
	}
	var (
		chunkFileBytes     []byte
		chunkFileErr       error
		relationsFileBytes []byte
		relationsErr       error
	)

	handlers := make([]func() error, 0)

	if request.ChunkFileKey != "" {
		handlers = append(handlers, func() error {
			chunkFileCompressData, err := tosManager.DownloadFileFromTos(ctx, request.ChunkFileKey)
			if err != nil {
				chunkFileErr = err
				return chunkFileErr
			}
			chunkFileData, err := merklet.GzipDecompress(chunkFileCompressData)
			if err != nil {
				chunkFileErr = err
				return chunkFileErr
			}
			chunkFileBytes = chunkFileData
			return nil
		})
	}

	if request.RelationsFileKey != "" {
		handlers = append(handlers, func() error {
			relationsFileCompressData, err := tosManager.DownloadFileFromTos(ctx, request.RelationsFileKey)
			if err != nil {
				relationsErr = err
				return relationsErr
			}
			relationsFileData, err := merklet.GzipDecompress(relationsFileCompressData)
			if err != nil {
				relationsErr = err
				return relationsErr
			}
			relationsFileBytes = relationsFileData
			return nil
		})
	}

	if len(handlers) > 0 {
		group.GoAndWait(handlers...)
	}

	if chunkFileErr != nil {
		logs.CtxError(ctx, "[MerkleUpload] get chunkFile error: %v", chunkFileErr)
		return nil, chunkFileErr
	}
	if relationsErr != nil {
		logs.CtxError(ctx, "[MerkleUpload] get relationsFile error: %v", relationsErr)
		return nil, relationsErr
	}

	resp, err := s.app.SemanticManager.BuildIndexWithUploadChunk(ctx, &codeEntity.UploadChunkRequest{
		Uid:                   uid,
		RepoName:              repoName,
		DeletedFileIds:        deleteFileIds,
		Branch:                branch,
		RepoPath:              repoPath,
		Did:                   did,
		Content:               chunkFileBytes,
		ChunkRelationContent:  relationsFileBytes,
		RootMerkleId:          rootMerkleId,
		OriginUserKnowledgeId: originUserKnowledgeId,
	})
	if err != nil {
		logs.CtxError(ctx, "[UploadMerkleTree] UploadChunk error: %v", err)
		return nil, err
	}
	logs.CtxInfo(ctx, "[UploadMerkleTree] resp: %v", resp)
	return resp, nil
}

// 查询索引构建记录
func (s *codesearchServiceImpl) QueryBuildRecord(ctx context.Context, request *codesearch.QueryBuildRecordRequest) (*codesearch.QueryBuildRecordResponse, error) {
	logs.CtxInfo(ctx, "[QueryBuildRecord] start request, repoName: %s, uid: %s, repoPath: %s, did: %s, branch: %s", request.RepoName, request.Uid, request.RepoPath, request.Did, request.Branch)
	userKnowledgeId := knowledgeUtils.GetDbUserKnowledgeId(request.RepoName, request.Uid, request.RepoPath, request.Did, request.Branch)

	logs.CtxInfo(ctx, "request userKnowledgeId: %s", userKnowledgeId)
	buildRecord, err := s.app.SemanticManager.QueryBuildRecord(ctx, &codeEntity.QueryBuildRecordRequest{
		UserKnowledgeId: userKnowledgeId,
	})
	if err != nil {
		logs.CtxError(ctx, "[QueryBuildRecord] QueryBuildRecord error: %v", err)
		return nil, err
	}

	return &codesearch.QueryBuildRecordResponse{
		RootMerkleId: buildRecord.RootMerkleId,
		BuildStatus:  strconv.Itoa(buildRecord.Status),
	}, nil
}

func (s *codesearchServiceImpl) BuildIndex(ctx context.Context, request *codesearch.BuildIndexRequest) (*codesearch.BuildIndexResponse, error) {
	defer func() {
		if r := recover(); r != nil {
			logs.CtxError(ctx, "[BuildIndex] panic recovered: %v\n%s", r, debug.Stack())
		}
	}()
	logs.CtxInfo(ctx, "[BuildIndex] start request, repoName: %s, repoURL: %s, branch: %s", request.RepoName, request.RepoURL, request.Branch)
	// 创建多个并发调用的处理器
	handlers := make([]func() error, 0)

	// 获取仓库配置
	repoConfig, ok := s.app.RepoManager.GetRepoItem(request.RepoName)
	if !ok {
		logs.CtxError(ctx, "get repo config error")
		return nil, errors.New("get repo config error")
	}
	newCtx := group.BuildAsyncCtx(ctx)
	group.Go(newCtx, 300*time.Minute, func(ctx context.Context) {

		// 初始化仓库
		_, err := s.app.WorkspaceManager.InitWorkspace(ctx, &workspaceEntity.RepoInfo{
			RepoName: request.RepoName,
			RepoURL:  request.RepoURL,
			Branch:   request.Branch,
		})
		if err != nil {
			logs.CtxError(ctx, "init workspace error: %v", err)
			return
		}

		// 构建 merkle tree
		treeFileMap, err := merklet.BuildMerkleTree(ctx, request.RepoName, repoConfig.PathList)
		if err != nil {
			logs.CtxError(ctx, "build merkle tree error: %v", err)
			return
		}
		treeFileBytes, err := json.Marshal(treeFileMap)
		if err != nil {
			logs.CtxError(ctx, "marshal treeFileMap error: %v", err)
			return
		}
		treeFile, err := merklet.GzipCompress(treeFileBytes)
		if err != nil {
			logs.CtxError(ctx, "gzip compress error: %v", err)
		}
		rootMerkleId := (*treeFileMap)["hash"].(string)
		// 先上传merkle tree
		_, err = s.app.SemanticManager.UploadMerkleTree(ctx, rootMerkleId, treeFile)
		if err != nil {
			logs.CtxError(ctx, "upload merkle tree error: %v", err)
			return
		}

		// 用于收集 BuildIndex 的结果
		var buildIndexErr error
		var createSummaryErr error
		var mu sync.Mutex

		// 添加 BuildIndex 调用
		handlers = append(handlers, func() error {
			logs.CtxInfo(ctx, "[BuildIndex] BuildIndexIfNeeded start, repoName: %s, repoURL: %s, branch: %s", request.RepoName, request.RepoURL, request.Branch)
			_, err := s.app.SemanticManager.BuildIndexIfNeeded(ctx, &semantic.BuildIndexParams{
				Uid:      "root",
				RepoPath: "user",
				Did:      "mac",
				RepoName: request.RepoName,
				RepoURL:  request.RepoURL,
				Branch:   request.Branch,
				Language: request.Language,
			}, &treeFile, treeFileMap, repoConfig.PathList)
			mu.Lock()
			logs.CtxInfo(ctx, "[BuildIndex] BuildIndexIfNeeded end, repoName: %s, repoURL: %s, branch: %s", request.RepoName, request.RepoURL, request.Branch)
			buildIndexErr = err
			mu.Unlock()
			if err != nil {
				logs.CtxError(ctx, "[BuildIndex] BuildIndex error: %v", err)
				return err
			}
			return nil
		})

		// 添加 CreateSummary 调用
		handlers = append(handlers, func() error {
			logs.CtxInfo(ctx, "[BuildIndex] CreateSummaryIfNeeded start, repoName: %s, repoURL: %s, branch: %s", request.RepoName, request.RepoURL, request.Branch)
			_, err := summary.CreateAndUploadSummaryIfNeeded(ctx, s.app, &summaryUpdate.GetSummaryDataRequest{
				Uid:      "root",
				RepoPath: "user",
				Did:      "mac",
				RepoName: request.RepoName,
				Branch:   request.Branch,
			}, request.RepoURL, rootMerkleId)
			mu.Lock()
			logs.CtxInfo(ctx, "[BuildIndex] CreateSummaryIfNeeded end, repoName: %s, repoURL: %s, branch: %s", request.RepoName, request.RepoURL, request.Branch)
			createSummaryErr = err
			mu.Unlock()
			if err != nil {
				logs.CtxError(ctx, "[BuildIndex] CreateSummary error: %v", err)
				return err
			}
			return nil
		})

		// 并发执行所有处理器
		err = group.GoAndWait(handlers...)
		if err != nil {
			// 记录具体的错误信息
			if buildIndexErr != nil {
				logs.CtxError(ctx, "[BuildIndex] BuildIndex error: %v", buildIndexErr)
			}
			if createSummaryErr != nil {
				logs.CtxError(ctx, "[BuildIndex] CreateSummary error: %v", createSummaryErr)
			}
		}
	})

	// GoAndWait 成功，返回收集的结果
	return &codesearch.BuildIndexResponse{}, nil
}

// 获取代码变更的情况下，摘要文件的更新情况
func (s *codesearchServiceImpl) GetSummaryUpdateFiles(ctx context.Context, request *codesearch.GetSummaryUpdateFilesRequest) (*codesearch.GetSummaryUpdateFilesResponse, error) {
	logs.CtxInfo(ctx, "[GetSummaryUpdateFiles] start request, repoName: %s, uid: %s, repoPath: %s, did: %s, branch: %s", request.RepoName, request.Uid, request.RepoPath, request.Did, request.Branch)
	return summary.GetSummaryUpdateFiles(ctx, s.app, request)
}

// 摘要触发更新
func (s *codesearchServiceImpl) UpdateSummary(ctx context.Context, request *codesearch.UpdateSummaryRequest) (*codesearch.UpdateSummaryResponse, error) {
	logs.CtxInfo(ctx, "[UpdateSummary] start request, repoName: %s, uid: %s, repoPath: %s, did: %s, branch: %s", request.RepoName, request.Uid, request.RepoPath, request.Did, request.Branch)
	return summary.UpdateSummary(ctx, s.app, request)
}

// 查询摘要的更新情况
func (s *codesearchServiceImpl) QuerySummaryBuildRecord(ctx context.Context, request *codesearch.QuerySummaryBuildRecordRequest) (*codesearch.QuerySummaryBuildRecordResponse, error) {
	logs.CtxInfo(ctx, "[QuerySummaryBuildRecord] start request, repoName: %s, uid: %s, repoPath: %s, did: %s, branch: %s", request.RepoName, request.Uid, request.RepoPath, request.Did, request.Branch)
	return summary.QuerySummaryBuildRecord(ctx, s.app, request)
}

func (s *codesearchServiceImpl) QueryBuildRepo(ctx context.Context, request *codesearch.QueryBuildRepoRequest) (*codesearch.QueryBuildRepoResponse, error) {
	resp, err := s.app.SemanticManager.QueryBuildRepo(ctx, request)
	if err != nil {
		logs.CtxError(ctx, "[QueryBuildRepo] QueryBuildRepo error: %v", err)
		return nil, err
	}
	return resp, nil
}
