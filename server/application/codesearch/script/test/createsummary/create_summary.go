package main

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"code.byted.org/ies/codin/application/codesearch/logic"
	"code.byted.org/ies/codin/application/codesearch/logic/module/filemanager"
	"code.byted.org/ies/codin/application/codesearch/logic/summary"
	knowledgeTools "code.byted.org/ies/codin/application/codesearch/logic/summary/tools/knowledge"
	"code.byted.org/ies/codin/application/codesearch/logic/tools"
	readDirTools "code.byted.org/ies/codin/application/codesearch/logic/tools"
	"code.byted.org/ies/codin/application/codesearch/repo/knowledge"
	knowledgeEntity "code.byted.org/ies/codin/application/codesearch/repo/knowledge/entity"
	"code.byted.org/ies/codin/application/codesearch/repo/plan/entity"
	plan "code.byted.org/ies/codin/application/codesearch/repo/plan/service"
	"code.byted.org/ies/codin/application/codesearch/repo/workspace"
	workspaceEntity "code.byted.org/ies/codin/application/codesearch/repo/workspace/entity"
	"code.byted.org/overpass/capcut_devops_codesearch/kitex_gen/codesearch"
)

func main() {
	ctx := context.Background()

	os.Setenv("ARK_KEY", "xxxxxxxxxxxxxxxxxxxxxxxx")
	os.Setenv("CODESUMMARY_SEED16_MODEL", "ep-20250714151539-s4vvm")
	app := logic.NewCodeSearch(ctx)

	repoItem, ok := app.RepoManager.GetRepoItem("ies/lvweb")
	if !ok {
		fmt.Println("error: 暂未添加该业务")
		return
	}
	_, err := app.WorkspaceManager.InitWorkspace(ctx, &workspaceEntity.RepoInfo{
		RepoName: repoItem.RepoName,
		RepoURL:  repoItem.RepoURL,
		Branch:   "master",
	})
	if err != nil {
		fmt.Println("error: ", err)
		return
	}
	// 插入默认根节点
	// rootModuleId := test_insert_root_module(ctx, app, "root", 3)
	// test_loop(ctx, app, rootModuleId)

	// test_missing_directory_check(ctx, app)

	// test_detect_path_overlaps()
	// test_read_dir(ctx, app, workspace)
	testCreateSummary(ctx, app)
	// testListDir(ctx, app, workspace, &codesearch.CreateSummaryRequest{
	// 	Business: business,
	// 	Platform: platform,
	// })

}

func testCreateSummary(ctx context.Context, app *logic.CodeSearch) {

	summary.CreateSummary(ctx, app, &codesearch.CreateSummaryRequest{
		RepoName: "ies/code-index",
		RepoURL:  "https://code.byted.org/ies/code-index",
		Branch:   "master",
	})

}

func test_insert_root_module(ctx context.Context, app *logic.CodeSearch, parentModuleId string, subModuleSize int) string {
	var subModules []knowledgeEntity.SubModuleInfo
	for i := 0; i < subModuleSize; i++ {
		subModules = append(subModules, knowledgeEntity.SubModuleInfo{
			Name: fmt.Sprintf("subModule%d", i),
			Path: fmt.Sprintf("/subModule%d", i),
		})
	}
	moduleParams := &knowledgeEntity.ModuleParams{
		ModulePath: parentModuleId,
		ModuleName: "project",
		ModuleDoc:  "项目root",
		SubModules: subModules,
	}
	knowledgeManager := knowledge.NewKnowledgeManager()
	knowledgeManager.SaveModule(ctx, moduleParams)
	return parentModuleId
}

func test_create_plan(ctx context.Context, app *logic.CodeSearch) string {
	tasks := []*entity.TaskRecord{
		{
			TaskID:     "CORE-1",
			Definition: "深入分析 /apps/dreamina/src/features 目录，重点关注业务功能模块的实现，包括AI相关功能(ai-agent, aigc-data-detail)、内容生成(content-generator)、发布(publish)等核心功能",
			TargetPath: "/apps/dreamina/src/features",
			Priority:   "high",
			TaskStatus: entity.TaskStatusPlanned,
			UpdatedAt:  time.Now(),
		},
		{
			TaskID:     "CORE-2",
			Definition: "深入分析 /apps/dreamina/src/services 目录，重点关注服务层的实现，包括账户(account)、配置(config)、性能(performance)等核心服务",
			TargetPath: "/apps/dreamina/src/services",
			Priority:   "high",
			TaskStatus: entity.TaskStatusPlanned,
		},
		{
			TaskID:     "CORE-3",
			Definition: "深入分析 /apps/dreamina/src/reports 目录，重点关注数据上报体系的实现",
			TargetPath: "/apps/dreamina/src/reports",
			Priority:   "medium",
			TaskStatus: entity.TaskStatusPlanned,
		},
	}
	planRecordManager := plan.NewPlanRecordManager()
	planID := planRecordManager.CreatePlan(ctx, tasks, "/apps/dreamina/src/")
	fmt.Printf("成功创建计划，计划ID: %s\n", planID)
	return planID
}

func test_record_knowledge(ctx context.Context, app *logic.CodeSearch, parentModuleId string, modulePath string) {
	knowledgeManager := knowledge.NewKnowledgeManager()
	moduleId, err := knowledgeTools.RecordKnowledge(ctx, knowledgeManager, &knowledgeTools.KnowledgeRecordParams{
		ModulePath: modulePath,
		ModuleName: "Dreamina Web入口模块",
		ModuleDoc:  "Dreamina Web应用的启动和初始化模块,负责应用的生命周期管理、服务注册、基础设施初始化等工作。采用分层架构设计,通过依赖注入框架管理服务,实现了一个可扩展的、模块化的启动流程。",
	})
	if err != nil {
		fmt.Println("记录知识失败: ", err)
		return
	}
	fmt.Println("记录知识成功: ", moduleId, modulePath)
}

func testListDir(ctx context.Context, app *logic.CodeSearch, workspace *workspace.TempWorkspace, request *codesearch.CreateSummaryRequest) {
	// 初始化业务和workspace
	workspace, err := summary.InitWorkspace(ctx, app, request)
	if err != nil {
		return
	}

	// 初始化临时管理器
	tempManager := summary.InitializeTempManager(ctx, app, request, workspace)
	content := tools.ListDir(ctx, app, tempManager.FileManager, &tools.ListDirParams{
		PathList: []string{"/"},
		Depth:    3,
		Reason:   "测试读取目录功能，查看content-generator目录的结构",
		RepoName: "ies/lvweb",
	})
	fmt.Println(content)
}

/**
 * @description 测试读取目录功能
 * @param ctx 上下文
 * @param app CodeSearch应用实例
 */
func test_read_dir(ctx context.Context, app *logic.CodeSearch, workspace *workspace.TempWorkspace) {
	p := &readDirTools.ReadDirParams{
		Path:     "/apps/dreamina/src/features/content-generator/",
		Reason:   "测试读取目录功能，查看content-generator目录的结构",
		RepoName: "ies/lvweb",
	}
	fileManager := filemanager.NewLocalFileManager(workspace.Path)

	content := readDirTools.ReadDir(ctx, app, &fileManager, p)
	fmt.Println("=== 目录读取结果 ===")
	fmt.Println(content)
}

/**
 * @description 初始化文件管理器
 * @param ctx 上下文
 * @param app CodeSearch应用实例
 * @param business 业务名称
 * @param platform 平台名称
 * @return *filemanager.FileManager 文件管理器
 * @return error 错误信息
 */
func initLocalFileManager(ctx context.Context, app *logic.CodeSearch, repoName string) (*filemanager.FileManager, error) {
	repoItem, ok := app.RepoManager.GetRepoItem(repoName)
	if !ok {
		return nil, fmt.Errorf("repo not found")
	}
	workspace, err := app.WorkspaceManager.InitWorkspace(ctx, &workspaceEntity.RepoInfo{
		RepoName: repoItem.RepoName,
		RepoURL:  repoItem.RepoURL,
		Branch:   "master",
	})
	if err != nil {
		return nil, err
	}
	fileManager := filemanager.NewLocalFileManager(workspace.Path)
	return &fileManager, nil
}

/**
 * @description 测试路径重叠检测功能
 */
func test_detect_path_overlaps() {
	paths := []string{
		"/apps/dreamina/src/features/content-generator",
		"/apps/dreamina/src/features/ai-agent",
		"/apps/dreamina/src/features/work-detail",
		"/apps/dreamina/server/caches",
		"/apps/dreamina/src/shared-business/aigc-data-operation-authority-manager",
	}

	// 这里需要导入missing_directory_check包来调用detectPathOverlaps
	// 由于包结构问题，我们先手动实现一个简单的测试
	fmt.Println("=== 路径重叠检测测试 ===")
	fmt.Println("输入的路径:")
	for _, path := range paths {
		fmt.Println("  ", path)
	}

	// 手动检查重叠
	var overlaps []string
	for i := 0; i < len(paths); i++ {
		for j := i + 1; j < len(paths); j++ {
			if isParentPath(paths[i], paths[j]) {
				overlapInfo := fmt.Sprintf("重叠: %s 是 %s 的父路径", paths[i], paths[j])
				overlaps = append(overlaps, overlapInfo)
			} else if isParentPath(paths[j], paths[i]) {
				overlapInfo := fmt.Sprintf("重叠: %s 是 %s 的父路径", paths[j], paths[i])
				overlaps = append(overlaps, overlapInfo)
			}
		}
	}

	if len(overlaps) > 0 {
		fmt.Println("检测到的重叠:")
		for _, overlap := range overlaps {
			fmt.Println("  ", overlap)
		}
	} else {
		fmt.Println("没有检测到重叠")
	}
}

/**
 * @description 检查path1是否是path2的父路径
 */
func isParentPath(path1, path2 string) bool {
	// 标准化路径分隔符
	path1 = strings.ReplaceAll(path1, "\\", "/")
	path2 = strings.ReplaceAll(path2, "\\", "/")

	// 移除开头的斜杠以便统一处理
	path1 = strings.TrimPrefix(path1, "/")
	path2 = strings.TrimPrefix(path2, "/")

	// 如果路径相同，不是父子关系
	if path1 == path2 {
		return false
	}

	// 如果path1为空，则path1是根目录，是任何路径的父路径
	if path1 == "" {
		return true
	}

	// 如果path2为空，则path2是根目录，不可能是其他路径的子路径
	if path2 == "" {
		return false
	}

	// 检查path2是否以path1开头，并且下一个字符是路径分隔符
	prefix := path1 + "/"
	return strings.HasPrefix(path2, prefix)
}
