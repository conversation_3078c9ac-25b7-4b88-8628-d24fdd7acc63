package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"time"

	"code.byted.org/ies/codin/application/codesearch/logic"
	"code.byted.org/ies/codin/application/codesearch/logic/code"
	"code.byted.org/ies/codin/application/codesearch/logic/module/filemanager"
	"code.byted.org/ies/codin/application/codesearch/logic/tools"
	"code.byted.org/ies/codin/application/codesearch/repo/workspace/entity"
	"code.byted.org/overpass/capcut_devops_codesearch/kitex_gen/codesearch"
)

func main() {
	ctx := context.Background()

	os.Setenv("ARK_KEY", "xxxxxxxxxxxxxxxxxxxx")
	os.Setenv("CODESEARCH_SEED16_MODEL", "ep-20250714151539-s4vvm")
	// os.Setenv("SEED16_PRO_MODEL", "ep-20250613154742-7h87q")
	app := logic.NewCodeSearch(ctx)

	// app.WorkspaceManager.InitWorkspace(ctx, &entity.RepoInfo{
	// 	RepoName:  repoItem.RepoName,
	// 	RepoURL:   repoItem.RepoURL,
	// 	ProjectID: repoItem.ProjectID,
	// 	Branch:    repoItem.Branch,
	// })

	// testFindFileName(ctx, app)
	// testFindFileNameByContent(ctx, app)
	// testListDir(ctx, app)
	// testReadFile(ctx, app)
	// testSemanticSearch(ctx, app)
	// testReferenceSearch(ctx, app)

	// testPrompt(ctx, app)
	testGetCode(ctx, app)
}

func testGetCode(ctx context.Context, app *logic.CodeSearch) {
	resp, err := code.FastGetCode(ctx, app, &codesearch.CodeSearchRequest{
		Query:    `搜索story-agent的启动流程`,
		Uid:      "f8eagf1b",
		RepoPath: "/Users/<USER>/Project/lvweb",
		Did:      "cff585ca-af91-58c7-9cb8-3fa56c28fd00",
		RepoName: "ies/lvweb",
		Branch:   "master",
		// PathList: []string{"/"},

		// Query:    `pippit lynx 启动流程的代码在哪里？`,
		// Business: "dreamina",
		// Platform: "server",
		// Mode:     &[]string{"fast"}[0],
	})
	if err != nil {
		fmt.Printf("---------------error-----------------\n")
	}
	WriteLogToFile(fmt.Sprintf("resp.Codes: %v", resp.Codes))
}

func testListDir(ctx context.Context, app *logic.CodeSearch) (string, error) {
	repoItem, ok := app.RepoManager.GetRepoItem("ies/lvweb")
	if !ok {
		fmt.Println("business or platform not found")
		return "", nil
	}
	workspace, err := app.WorkspaceManager.InitWorkspace(ctx, &entity.RepoInfo{
		RepoName: repoItem.RepoName,
		RepoURL:  repoItem.RepoURL,
		Branch:   "master",
	})
	if err != nil {
		fmt.Println("init workspace error")
		return "", nil
	}

	// 	// 使用VirtualTreeBuilder构造虚拟文件树
	// 	builder := filemanager.NewVirtualTreeBuilder(workspace.Path)

	// 	// 添加文件
	// 	files := map[string]string{
	// 		"packages/pippit/pippit-lynx-services/src/index.ts":                   "// 虚拟的入口文件\nconsole.log('Hello from virtual file');",
	// 		"packages/pippit/pippit-lynx-services/src/config.ts":                  "// 虚拟的配置文件\nexport const config = { apiUrl: 'https://api.example.com' };",
	// 		"packages/pippit/pippit-lynx-services/src/types.ts":                   "// 虚拟的类型定义\nexport interface User { id: string; name: string; }",
	// 		"packages/pippit/pippit-lynx-services/src/services/auth-service.ts":   "// 虚拟的认证服务\nclass AuthService { login() { return 'login'; } }",
	// 		"packages/pippit/pippit-lynx-services/src/services/user-service.ts":   "// 虚拟的用户服务\nclass UserService { getUser() { return 'user'; } }",
	// 		"packages/pippit/pippit-lynx-services/src/services/internal/utils.ts": "// 虚拟的工具函数\nexport function helper() { return 'helper'; }",
	// 		"packages/pippit/pippit-lynx-services/src/components/Button.tsx":      "// 虚拟的按钮组件\nimport React from 'react';\nconst Button = () => <button>Click me</button>;",
	// 		"packages/pippit/pippit-lynx-services/src/components/Modal.tsx":       "// 虚拟的模态框组件\nimport React from 'react';\nconst Modal = () => <div>Modal content</div>;",
	// 		"README.md": "# 虚拟项目\n这是一个虚拟文件管理器的测试项目。",
	// 		"package.json": `{
	//   "name": "virtual-test-project",
	//   "version": "1.0.0",
	//   "dependencies": {
	//     "react": "^18.0.0",
	//     "typescript": "^4.9.0"
	//   }
	// }`,
	// 	}

	// 	if err := builder.AddFiles(files); err != nil {
	// 		fmt.Printf("添加文件失败: %v\n", err)
	// 		return "", err
	// 	}

	// 	// 构建虚拟目录树
	// 	virtualTree := builder.Build()

	p := &tools.ListDirParams{
		PathList: []string{"packages/pippit/pippit-lynx-services/src/"},
		RepoName: "ies/lvweb",
		Depth:    2, // 增加深度以测试递归
	}

	fileManager := filemanager.NewLocalFileManager(workspace.Path)
	// fileManager := filemanager.NewVirtualFileManager(workspace.Path, virtualTree)
	content := tools.ListDir(ctx, app, &fileManager, p)
	fmt.Println("=== 虚拟文件管理器测试结果 ===")
	fmt.Println(content)
	WriteLogToFile(content)
	return content, nil
}

func testFindFileName(ctx context.Context, app *logic.CodeSearch) (string, error) {
	// 初始化文件管理器
	fileManager, err := initLocalFileManager(ctx, app, &entity.RepoInfo{
		RepoName: "ies/lvweb",
		Branch:   "master",
		RepoURL:  "https://code.byted.org/ies/lvweb.git",
	})
	if err != nil {
		return "", err
	}

	p := &tools.FindFileNameParams{
		Path: "/apps/dreamina/src/features/content-generator/internal/features/image-generator/components",
		Glob: "*text-enhancement*",
	}
	content := tools.FindFileName(ctx, app, fileManager, p)
	WriteLogToFile(content)
	return content, nil
}

func testFindFileNameByContent(ctx context.Context, app *logic.CodeSearch) (string, error) {
	// 初始化文件管理器
	fileManager, err := initLocalFileManager(ctx, app, &entity.RepoInfo{
		RepoName: "ies/lvweb",
		Branch:   "master",
		RepoURL:  "https://code.byted.org/ies/lvweb.git",
	})
	if err != nil {
		return "", err
	}

	p := &tools.FindFileNameByContentParams{

		Path:  "apps/dreamina/src/features/content-generator/internal/features/image-generator/components",
		Regex: "text.*enhance",
		Depth: 2,
	}
	content := tools.FindFileNameByContent(ctx, app, fileManager, p)
	fmt.Println("content: ", content)
	WriteLogToFile(content)
	return content, nil
}

func testReadFile(ctx context.Context, app *logic.CodeSearch) (string, error) {
	// 初始化文件管理器
	fileManager, err := initLocalFileManager(ctx, app, &entity.RepoInfo{
		RepoName: "ies/lvweb",
		Branch:   "master",
		RepoURL:  "https://code.byted.org/ies/lvweb.git",
	})
	if err != nil {
		return "", err
	}

	p := &tools.ReadFileParams{
		PathList:  []string{"/dreamina/apps/lv-components/src/components/Upload/interface.ts"},
		StartLine: 1,
		EndLine:   250,
	}
	fileInfoList, err := tools.ReadFile(ctx, app, fileManager, p)
	if err != nil {
		return "", err
	}
	WriteLogToFile(fileInfoList[0].Content)
	return fileInfoList[0].Content, nil

}

func testSemanticSearch(ctx context.Context, app *logic.CodeSearch) (string, error) {

	resp, err := code.GetSemanticCode(ctx, app, &codesearch.CodeSearchRequest{
		Query:    "启动流程在哪里",
		Uid:      "dbce483e",
		Did:      "743a7362-d7fe-5687-b617-f1aa213a38ca",
		RepoName: "ies/lvweb",
		Branch:   "master",
		RepoPath: "/Users/<USER>/dev/lvweb",
	})
	if err != nil {
		fmt.Println("error: ", err)
		return "", err
	}
	fmt.Println("Codes size: ", len(resp.Codes))
	respJSON, _ := json.Marshal(resp)
	WriteLogToFile(string(respJSON))
	return string(respJSON), nil
}

func testVirtualListDir(ctx context.Context, app *logic.CodeSearch, repoInfo *entity.RepoInfo) (string, error) {
	workspace, err := app.WorkspaceManager.InitWorkspace(ctx, repoInfo)
	if err != nil {
		fmt.Println("init workspace error")
		return "", nil
	}

	// 使用VirtualTreeBuilder构造虚拟文件树
	builder := filemanager.NewVirtualTreeBuilder(workspace.Path)

	// 添加文件
	files := map[string]string{
		"packages/pippit/pippit-lynx-services/src/index.ts":                   "// 虚拟的入口文件\nconsole.log('Hello from virtual file');",
		"packages/pippit/pippit-lynx-services/src/config.ts":                  "// 虚拟的配置文件\nexport const config = { apiUrl: 'https://api.example.com' };",
		"packages/pippit/pippit-lynx-services/src/types.ts":                   "// 虚拟的类型定义\nexport interface User { id: string; name: string; }",
		"packages/pippit/pippit-lynx-services/src/services/auth-service.ts":   "// 虚拟的认证服务\nclass AuthService { login() { return 'login'; } }",
		"packages/pippit/pippit-lynx-services/src/services/user-service.ts":   "// 虚拟的用户服务\nclass UserService { getUser() { return 'user'; } }",
		"packages/pippit/pippit-lynx-services/src/services/internal/utils.ts": "// 虚拟的工具函数\nexport function helper() { return 'helper'; }",
		"packages/pippit/pippit-lynx-services/src/components/Button.tsx":      "// 虚拟的按钮组件\nimport React from 'react';\nconst Button = () => <button>Click me</button>;",
		"packages/pippit/pippit-lynx-services/src/components/Modal.tsx":       "// 虚拟的模态框组件\nimport React from 'react';\nconst Modal = () => <div>Modal content</div>;",
		"README.md": "# 虚拟项目\n这是一个虚拟文件管理器的测试项目。",
		"package.json": `{
	  "name": "virtual-test-project",
	  "version": "1.0.0",
	  "dependencies": {
	    "react": "^18.0.0",
	    "typescript": "^4.9.0"
	  }
	}`,
	}

	if err := builder.AddFiles(files); err != nil {
		fmt.Printf("添加文件失败: %v\n", err)
		return "", err
	}

	// 构建虚拟目录树
	virtualTree := builder.Build()

	p := &tools.ListDirParams{
		PathList: []string{"packages/pippit/pippit-lynx-services/src/"},
		Depth:    2, // 增加深度以测试递归
	}

	fileManager := filemanager.NewVirtualFileManager(workspace.Path, virtualTree)
	content := tools.ListDir(ctx, app, &fileManager, p)
	fmt.Println("=== 虚拟文件管理器测试结果 ===")
	fmt.Println(content)
	WriteLogToFile(content)
	return content, nil
}

// func testPrompt(ctx context.Context, app *logic.CodeSearch) {

// 	code := `
// 	 Based on the code analysis, I'll create a structured summary of the startup flow:

// {
//   "Records": [
//     {
//       "Path": "/apps/dreamina/src/entry/pc.ts",
//       "Summary": "Main entry point that initializes the application flow. Prevents multiple initializations during history navigation and handles startup errors.",
//       "Reason": "Contains the primary entry point and initialization logic"
//     },
//     {
//       "Path": "/apps/dreamina/src/entry/browser/flow/index.ts",
//       "Summary": "Core initialization flow that sets up dependency injection, services, and executes the lifecycle phases (Start -> UserInit -> Prepare -> Render -> Completed). Handles service registration, i18n, monitoring, and environment setup.",
//       "Reason": "Contains the detailed startup flow implementation"
//     },
//     {
//       "Path": "/apps/dreamina/src/entry/job/frame/frame-job.tsx",
//       "Summary": "Frame initialization job that handles UI theme, routing, domain configuration, beta features, websocket setup and commercial features loading. Executes in phases: Prepare, Render and Completed.",
//       "Reason": "Manages core framework initialization"
//     }
//   ],
//   "Doc": "## 项目概述\n- Web端AI内容生成平台\n- 基于React的SSR架构\n- 使用依赖注入实现模块化\n\n## 核心功能\n- 应用启动流程管理\n- 服务注册与初始化\n- 生命周期管理\n- 国际化支持\n- 性能监控\n\n## 代码结构\n- entry/pc.ts: 主入口文件\n- entry/browser/flow: 浏览器端初始化流程\n- entry/job: 启动任务管理\n- entry/task: 具体初始化任务\n\n## 集成关系\n- 依赖注入容器管理服务实例\n- Job系统管理启动流程\n- 生命周期分5个阶段执行\n- 集成监控和性能追踪\n\n## 配置和部署\n- 支持国内外不同环境配置\n- Beta功能管理\n- CDN域名配置\n- 商业化特性加载\n\n# 注意事项\n- 启动流程按照严格的生命周期执行\n- 服务注册顺序很重要\n- 需要处理SSR场景下的特殊情况\n- 包含性能监控和错误上报"
// }
// 	`

// 	extractedJSON, extractErr := commonUtils.ExtractJSONFromResponse(code)
// 	if extractErr != nil {
// 		fmt.Println("extractErr: ", extractErr)
// 	}
// 	fmt.Println("extractedJSON: ", extractedJSON)
// }

// WriteLogToFile 将日志内容写入本地文件，每条新起一行
func WriteLogToFile(logContent string) {
	logFile := "codesearch.log"
	f, err := os.OpenFile(logFile, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return
	}
	defer f.Close()
	line := time.Now().Format("2006-01-02 15:04:05") + " " + logContent + "\n"
	f.WriteString(line)
}

/**
 * @description 初始化文件管理器
 * @param ctx 上下文
 * @param app CodeSearch应用实例
 * @return *filemanager.FileManager 文件管理器
 * @return error 错误信息
 */
func initLocalFileManager(ctx context.Context, app *logic.CodeSearch, repoInfo *entity.RepoInfo) (*filemanager.FileManager, error) {
	workspace, err := app.WorkspaceManager.InitWorkspace(ctx, repoInfo)
	if err != nil {
		return nil, err
	}
	fileManager := filemanager.NewLocalFileManager(workspace.Path)
	return &fileManager, nil
}
