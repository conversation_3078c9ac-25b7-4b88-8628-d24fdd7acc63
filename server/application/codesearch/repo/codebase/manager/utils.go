package manager

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"code.byted.org/flow/datamind-code-index/dao"
	"code.byted.org/flow/datamind-code-index/model"
	"code.byted.org/flow/datamind-code-index/util"
	"code.byted.org/gopkg/logs/v2"
	"gorm.io/gorm"
)

// 定义中间结构体
type JsonRelation struct {
	SourceChunkId string `json:"sourceChunkId"`
	TargetChunkId string `json:"targetChunkId"`
	Type          string `json:"type"`
	SubType       string `json:"subType"`
}

// Entity 定义 JSON 文件对应的结构体
type Entity struct {
	Attributes map[string]interface{} `json:"attributes"`
	EntityID   string                 `json:"entityID"`
	ID         string                 `json:"id"`
	FileHash   string                 `json:"fileHash"`
	Name       string                 `json:"name"`
	Type       string                 `json:"type"`
	URIID      int                    `json:"uriID"`
}

func UploadTempChunksFromJSONFile(ctx context.Context, db *gorm.DB, fileContent []byte, userKnowledgeId string, knowledgebaseId string) error {
	// 解析 JSON 数据
	var entities []Entity
	err := json.Unmarshal(fileContent, &entities)
	if err != nil {
		return err
	}

	chunks := make([]model.TempChunk, 0)
	for _, entity := range entities {
		// 不处理 file 类型. 因为 file 的 content 现在太简单了 LLM 压根抽不出什么实体出来。 通过 prompt 词约束在处理其他代码块的时候稳定抽出 file entity 即可。
		// 所以这里可以省掉 chunk 的部分.
		// 这里虽然不期望它抽出来实体，但是因为 relationship 要做到。。所以不能过滤..
		// if entity.Type == "file" {
		// 	continue
		// }
		context, err := BuildChunkContent(entity.EntityID, entity.Type, entity.Attributes)
		if err != nil {
			return err
		}
		// 转换数据
		hash := util.Sha256Hash(context)
		chunk := model.TempChunk{
			PartitionKey:    dao.GetPartitionKey(userKnowledgeId),
			UserKnowledgeId: userKnowledgeId,
			KnowledgebaseId: knowledgebaseId,
			ChunkID:         entity.ID,
			ChunkType:       entity.Type,
			Content:         context,
			ContentHash:     hash,
			GMTCreate:       time.Now(),
			GMTModified:     time.Now(),
			FileId:          entity.FileHash,
			PathList:        util.GetPathList(entity.ID),
		}

		if strings.HasPrefix(entity.ID, "/") {
			//throw error
			return errors.New(fmt.Sprintf("chunk id must start with ./, chunk id is %s", entity.ID))
		}

		if len(context) > 1000000 {
			logs.CtxInfo(ctx, "chunk content is too long, entityID=%s, len=%d", entity.EntityID, len(context))
			//throw error
			return errors.New(fmt.Sprintf("chunk content is too long %s", context))
		}
		chunks = append(chunks, chunk)
		if len(chunks) == 1000 {
			result := db.Create(&chunks)
			if result.Error != nil {
				logs.CtxInfo(ctx, "insert chunk error: %v", result.Error)
				return result.Error
			}
			logs.CtxInfo(ctx, "Inserted 1000 chunks")
			chunks = make([]model.TempChunk, 0)
		}
	}
	if len(chunks) > 0 {
		result := db.Create(&chunks)
		if result.Error != nil {
			logs.CtxInfo(ctx, "insert chunk error: %v", result.Error)
			return result.Error
		}
		logs.CtxInfo(ctx, "Inserted %v chunks", result.RowsAffected)
	}
	return nil
}

func UploadTempRelationshipFromJSONFile(ctx context.Context, db *gorm.DB, fileContent []byte, userKnowledgeId string, knowledgebaseId string) error {
	var jsonRelations []JsonRelation
	err := json.Unmarshal(fileContent, &jsonRelations)
	if err != nil {
		return err
	}

	partitionKey := dao.GetPartitionKey(userKnowledgeId)
	// 先删后增
	db.Exec(dao.DeleteRelationSQL, partitionKey, userKnowledgeId)

	currentTime := time.Now()

	relations := make([]model.ChunkRelation, len(jsonRelations))
	for i, rel := range jsonRelations {
		relations[i] = model.ChunkRelation{
			SourceChunkId:   rel.SourceChunkId,
			TargetChunkId:   rel.TargetChunkId,
			RelationType:    rel.Type, // 或 rel.Type
			RelationSubType: rel.SubType,
			GMTCreate:       currentTime,
			GMTModified:     currentTime,
			KnowledgebaseId: knowledgebaseId,
			UserKnowledgeId: userKnowledgeId,
			PartitionKey:    partitionKey,
		}
	}

	if len(relations) > 0 {
		result := db.Create(&relations)
		if result.Error != nil {
			//print error
			logs.CtxError(ctx, fmt.Sprintf("Failed to create relation table: %v", result.Error))
			return result.Error
		}
		//print affected rows
		logs.CtxInfo(ctx, fmt.Sprintf("UpsertChunkRelation success, affected rows: %v", result.RowsAffected))
	}
	return nil
}

// 区分db是因为db有byted.org: 前缀，主要是因为有冒号，tos上不能有冒号，所以临时态先区分一下
func GetDbUserKnowledgeId(repoName string, uid string, path string, did string, branch string) string {
	switch repoName {
	// case "ies/lvweb":
	// 	repoName = "ies/dreamina"
	case "ies/lv-lynx":
		repoName = "ies/dreamina-lynx"
	}
	return fmt.Sprintf("%s/%s/%s/code.byted.org:%s/tree/%s", uid, path, did, repoName, branch)
}

func GetDbKnowledgebaseId(repoName string) string {
	switch repoName {
	// case "ies/lvweb":
	// 	repoName = "ies/dreamina"
	case "ies/lv-lynx":
		repoName = "ies/dreamina-lynx"
	}
	return fmt.Sprintf("code.byted.org:%s", repoName)
}

// todo(liboti)
// 所有特化的配置都收口在这里
func GetUserKnowledgeId(repoName string, uid string, path string, did string, branch string) string {
	switch repoName {
	// case "ies/lvweb":
	// 	repoName = "ies/dreamina"
	case "ies/lv-lynx":
		repoName = "ies/dreamina-lynx"
	}
	return fmt.Sprintf("%s/%s/%s/code.byted.org/%s/tree/%s", uid, path, did, repoName, branch)
}

func GetKnowledgeBranchId(repoName string, branch string) string {
	switch repoName {
	// case "ies/lvweb":
	// 	repoName = "ies/dreamina"
	case "ies/lv-lynx":
		repoName = "ies/dreamina-lynx"
	}
	knowledgeBranchId := fmt.Sprintf("code.byted.org/%s/%s", repoName, branch)
	return knowledgeBranchId
}

func GetKnowledgebaseId(repoName string) string {
	switch repoName {
	// case "ies/lvweb":
	// 	repoName = "ies/dreamina"
	case "ies/lv-lynx":
		repoName = "ies/dreamina-lynx"
	}
	return fmt.Sprintf("code.byted.org/%s", repoName)
}

// func GetPathList(repoName string, pathList *[]string) []string {
// 	if pathList != nil && len(*pathList) != 0 {
// 		return *pathList
// 	}
// 	if repoName == "ies/lvweb" {
// 		return []string{"apps/dreamina"}
// 	}
// 	if repoName == "ies/lv-lynx" {
// 		return []string{"apps/dreamina-lynx", "packages/dreamina-services", "packages/ai-dreamina"}
// 	}
// 	return []string{"/"}
// }
