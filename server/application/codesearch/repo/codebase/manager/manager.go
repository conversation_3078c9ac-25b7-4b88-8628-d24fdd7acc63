package manager

import (
	"context"
	"errors"
	"os"
	"sync"

	"code.byted.org/flow/datamind-code-index/dao"
	"code.byted.org/flow/datamind-code-index/model"
	"code.byted.org/flow/datamind-code-index/service"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/overpass/capcut_devops_codesearch/kitex_gen/codesearch"
	"gorm.io/gorm"

	"code.byted.org/ies/codin/application/codesearch/entity/code"
	"code.byted.org/ies/codin/application/codesearch/repo/codebase/config"
	"code.byted.org/ies/codin/common/group"
	"code.byted.org/ies/codin/common/tcc"
)

var (
	dbInstance *gorm.DB  // 全局保存数据库连接
	dbOnce     sync.Once // 保证线程安全的初始化
	dbInitErr  error     // 记录初始化错误
)

// Manager 代码库管理器
type Manager struct {
	// 这里可以添加需要的依赖，比如数据库连接等
	client *dao.DBService
}

// NewManager 创建新的代码库管理器
func NewManager() *Manager {
	dbService := dao.DBService{}
	return &Manager{
		client: &dbService,
	}
}

// InitDefaultDB 获取或创建数据库连接（单例模式）
func (m *Manager) InitDefaultDB(ctx context.Context) (*gorm.DB, error) {
	dbOnce.Do(func() {
		dbInstance, dbInitErr = m.initDB(ctx)
	})

	if dbInitErr != nil {
		return nil, dbInitErr
	}

	// 检查连接是否有效
	if err := dbInstance.Exec("SELECT 1").Error; err != nil {
		// 连接已失效，重新创建
		if dbInstance, dbInitErr = m.initDB(ctx); dbInitErr != nil {
			return nil, dbInitErr
		}
	}

	return dbInstance, nil
}

// GetCode 根据关键词获取匹配的代码片段
func (m *Manager) GetCode(ctx context.Context, req *code.GetCodeRequest) (*model.HybridQueryResult, error) {
	db, err := m.InitDefaultDB(ctx)
	if err != nil {
		return nil, err
	}
	queryService := service.NewQueryService(db)
	logs.CtxInfo(ctx, "GetCode userKnowledgeId: %s, query: %s", req.UserKnowledgeId, req.Query)
	param := model.HybridQueryParam{
		Query:                   req.Query,
		EmbeddingModelConfig:    *model.NewEmbeddingModelConfig(),
		UserKnowledgeId:         req.UserKnowledgeId,
		NaiveTopKChunks:         25,
		EntityRelatedTopKChunks: 170,
		TopKEntities:            128,
		TopKChunks:              30,
	}

	// 现在db数据都是错的 先注释
	// 检查 PathList 是否为 ["/"] 如果为 “/” 则不进行过滤
	// if !(len(req.PathList) == 1 && req.PathList[0] == "/") {
	// 	param.PathList = req.PathList
	// }

	result, err := queryService.HybridQuerySimilarChunks(ctx, param)

	if err != nil {
		return nil, err
	}

	return &result, nil
}

// GetChunksByIDs 根据ChunkIDs批量查询chunks信息
func (m *Manager) GetChunksByIDs(ctx context.Context, chunkIDs []string, userKnowledgeId string) (map[string]model.Chunk, error) {
	if len(chunkIDs) == 0 {
		return make(map[string]model.Chunk), nil
	}

	db, err := m.InitDefaultDB(ctx)
	if err != nil {
		return nil, err
	}

	partitionKey := dao.GetPartitionKey(userKnowledgeId)

	// 分批处理，每批最多处理500个chunkID，避免SQL IN查询参数过多
	const batchSize = 500
	chunkMap := make(map[string]model.Chunk)
	var mapMutex sync.Mutex
	totalFound := 0

	// 创建批次
	var batches [][]string
	for i := 0; i < len(chunkIDs); i += batchSize {
		end := i + batchSize
		if end > len(chunkIDs) {
			end = len(chunkIDs)
		}
		batches = append(batches, chunkIDs[i:end])
	}

	// 并发查询所有批次
	handlers := make([]func() error, 0, len(batches))

	for batchIndex, batchChunkIDs := range batches {
		batchIdx := batchIndex
		batchIDs := batchChunkIDs

		handlers = append(handlers, func() error {
			// 构建SQL查询语句，批量查询chunks
			var chunks []model.Chunk
			result := db.Debug().Table("chunk").
				Where("partition_key = ? AND user_knowledge_id = ? AND chunk_id IN ?",
					partitionKey, userKnowledgeId, batchIDs).
				Find(&chunks)

			if result.Error != nil {
				logs.CtxError(ctx, "分批查询chunks失败: %v, userKnowledgeId: %s, partitionKey: %s, batchSize: %d, batchIndex: %d",
					result.Error, userKnowledgeId, partitionKey, len(batchIDs), batchIdx)
				return result.Error
			}

			// 线程安全地将当前批次的结果合并到总的chunkMap中
			mapMutex.Lock()
			for _, chunk := range chunks {
				chunkMap[chunk.ChunkID] = chunk
			}
			totalFound += len(chunks)
			mapMutex.Unlock()

			logs.CtxInfo(ctx, "分批查询chunks成功, batchIndex: %d, batchSize: %d, foundInBatch: %d",
				batchIdx, len(batchIDs), len(chunks))

			return nil
		})
	}

	// 并发执行所有批次查询
	err = group.GoAndWait(handlers...)
	if err != nil {
		logs.CtxError(ctx, "并发查询chunks失败: %v, userKnowledgeId: %s", err, userKnowledgeId)
		return nil, err
	}

	logs.CtxInfo(ctx, "并发批量查询chunks完成, totalRequestCount: %d, totalFoundCount: %d, batchCount: %d, userKnowledgeId: %s",
		len(chunkIDs), totalFound, len(batches), userKnowledgeId)

	return chunkMap, nil
}

// GetCodeDependency 根据依赖关系获取匹配的代码片段
func (m *Manager) GetCodeDependency(ctx context.Context, req *code.GetCodeDependencyRequest) (*[]model.QueryChunkRelation, error) {
	db, err := m.InitDefaultDB(ctx)
	if err != nil {
		return nil, err
	}
	codeChunkService := service.NewCodeChunkService(db, ctx)
	result, err := codeChunkService.QueryChunkDependencies(req.UserKnowledgeId, req.ChunkId, string(req.RelationType), string(req.RelationSubType))
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// GetCodeReference 根据引用关系获取匹配的代码片段
func (m *Manager) GetCodeReference(ctx context.Context, req *code.GetCodeDependencyRequest) (*[]model.QueryChunkRelation, error) {
	db, err := m.InitDefaultDB(ctx)
	if err != nil {
		return nil, err
	}
	codeChunkService := service.NewCodeChunkService(db, ctx)
	result, err := codeChunkService.QueryChunkReferences(req.UserKnowledgeId, req.ChunkId, string(req.RelationType), string(req.RelationSubType))
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// UploadChunk 上传chunk
func (m *Manager) BuildIndexWithUploadChunk(ctx context.Context, req *code.UploadChunkRequest) (*codesearch.UploadMerkleTreeResponse, error) {
	db, err := m.InitDefaultDB(ctx)
	if err != nil {
		logs.CtxError(ctx, "InitDefaultDB() error: %v", err)
		return nil, err
	}
	userKnowledgeId := GetDbUserKnowledgeId(req.RepoName, req.Uid, req.RepoPath, req.Did, req.Branch)
	knowledgebaseId := GetDbKnowledgebaseId(req.RepoName)
	originUserKnowledgeId := req.OriginUserKnowledgeId
	rootMerkleId := req.RootMerkleId
	logs.CtxInfo(ctx, "userKnowledgeId: %s, knowledgebaseId: %s, req.DeletedFileIds: %s, rootMerkleId: %s", userKnowledgeId, knowledgebaseId, req.DeletedFileIds, rootMerkleId)

	if len(req.Content) > 0 {
		// 上传 chunk
		err = UploadTempChunksFromJSONFile(ctx, db, req.Content, userKnowledgeId, knowledgebaseId)
		if err != nil {
			logs.CtxError(ctx, "upload chunk error: %v", err)
			return nil, err
		}
	}

	if len(req.ChunkRelationContent) > 0 {
		// 上传relationship
		err = UploadTempRelationshipFromJSONFile(ctx, db, req.ChunkRelationContent, userKnowledgeId, knowledgebaseId)
		if err != nil {
			logs.CtxError(ctx, "upload relationship error: %v", err)
			return nil, err
		}
	}

	logs.CtxInfo(ctx, "upload chunk and relationship success")
	llmConfig := *model.NewLlmConfig()
	apiKey := ""
	if env.IsPPE() || env.IsProduct() {
		apiKey, err = tcc.GetTccReader().GetArkApiKey(ctx)
		if err != nil {
			return nil, err
		}
	} else {
		apiKey = os.Getenv("ARK_KEY")
	}
	llmConfig.ApiKey = apiKey
	llmConfig.ModelName = "volcark/ep-20250714151539-s4vvm"
	llmConfig.NullOnFailure = true
	llmConfig.OutputJson = true
	llmConfig.Temperature = 0.0
	llmConfig.BatchSize = 320
	llmConfig.ThinkingMode = "disabled"

	embeddingConfig := *model.NewEmbeddingModelConfig()
	extractEntityConfig := *model.NewExtractEntityConfig()
	extractEntityConfig.Prompt = config.ExtractEntityDefaultPrompt
	extractEntityConfig.Concurrency = 80
	summarizeConfig := *model.NewSummarizeEntityConfig()
	summarizeConfig.Concurrency = 80
	summarizeConfig.BatchSize = 1000

	id, err := service.NewCodeIndexWriteService(db, ctx).BuildIndex(
		userKnowledgeId,
		originUserKnowledgeId,
		knowledgebaseId,
		req.Branch,
		rootMerkleId,
		req.DeletedFileIds,
		true,
		false,
		summarizeConfig,
		llmConfig,
		embeddingConfig,
		extractEntityConfig,
	)
	if id == 0 {
		logs.CtxError(ctx, "BuildIndex() id")
		return nil, err
	}

	if err != nil {
		logs.CtxError(ctx, "BuildIndex() error = %v", err)
		return nil, err
	}

	logs.CtxInfo(ctx, "BuildIndex success")
	return &codesearch.UploadMerkleTreeResponse{
		Id: userKnowledgeId,
	}, nil
}

func (m *Manager) QueryBuildRecord(ctx context.Context, req *code.QueryBuildRecordRequest) (*model.BuildRecord, error) {
	db, err := m.InitDefaultDB(ctx)
	if err != nil {
		return nil, err
	}
	buildRecordService := service.NewBuildService(db, ctx)
	// 这里需要精确匹配 userKnowledgeId  knowledgebaseId 和 branch 不填
	logs.CtxInfo(ctx, "QueryBuildRecord userKnowledgeId: %s", req.UserKnowledgeId)
	buildRecord, err := buildRecordService.QueryLatestRelatedBuildRecord("", req.UserKnowledgeId, "")
	if err != nil {
		return nil, err
	}
	logs.CtxInfo(ctx, "After QueryBuildRecord RootMerkleId: %s, id: %d, userKnowledgeId: %s", buildRecord.RootMerkleId, buildRecord.ID, buildRecord.UserKnowledgeId)
	return buildRecord, nil
}

func (m *Manager) FuzzyQueryBuildRecord(ctx context.Context, req *code.FuzzyQueryBuildRecordRequest) (*model.BuildRecord, error) {
	db, err := m.InitDefaultDB(ctx)
	if err != nil {
		return nil, err
	}

	buildRecordService := service.NewBuildService(db, ctx)
	// 这里需要精确匹配 userKnowledgeId  knowledgebaseId 和 branch 不填
	logs.CtxInfo(ctx, "QueryBuildRecord userKnowledgeId: %s, knowledgebaseId: %s, branch: %s", req.UserKnowledgeId, req.KnowledgebaseId, req.Branch)
	buildRecords, err := buildRecordService.QueryRelatedBuildRecords(req.KnowledgebaseId, req.UserKnowledgeId, req.Branch)
	if err != nil {
		return nil, err
	}
	if len(buildRecords) == 0 {
		return nil, errors.New("build record not found")
	}
	logs.CtxInfo(ctx, "After QueryBuildRecord RootMerkleId: %s, id: %d, userKnowledgeId: %s", buildRecords[0].RootMerkleId, buildRecords[0].ID, buildRecords[0].UserKnowledgeId)
	return &buildRecords[0], nil
}

func (m *Manager) ExistsBuildRecord(ctx context.Context, req *code.FuzzyQueryBuildRecordRequest) (bool, error) {
	exists, err := m.existsBuildRecord(ctx, req.UserKnowledgeId, model.IndexStatusIndexing)
	if err != nil {
		return false, err
	}
	exists, err = m.existsBuildRecord(ctx, req.UserKnowledgeId, model.IndexStatusSuccess)
	return exists, nil
}

func (m *Manager) QueryBuildRepo(ctx context.Context, req *code.QueryBuildRepoRequest) (*code.QueryBuildRepoResponse, error) {
	db, err := m.InitDefaultDB(ctx)
	if err != nil {
		return nil, err
	}
	var results []code.QueryBuildDBRepoItem
	result := db.Raw(`
	SELECT
  *
	FROM
		(
			SELECT
				*,
				ROW_NUMBER() OVER(
					PARTITION BY knowledgebase_id
					ORDER BY
						GMT_create DESC
				) AS rn
			FROM
				code_index_512_16.build_record
		) t
	WHERE
  rn = 1;
	`).Scan(&results)
	if result.Error != nil {
		log.V2.Error().With(ctx).Str("查询 record_repository 失败").Error(result.Error).KVs().Emit()
		return nil, result.Error
	}

	items := make([]*code.QueryBuildRepoItem, 0, len(results))
	for i := range results {
		items = append(items, &code.QueryBuildRepoItem{
			RepoName: results[i].KnowledgebaseId,
			Branch:   results[i].Branch,
			Language: "",
			Uid:      "",
			Status:   results[i].Status,
			CreateAt: results[i].GMTCreate.Format("2006-01-02 15:04:05"),
			ModifyAt: results[i].GMTModified.Format("2006-01-02 15:04:05"),
		})
	}
	return &code.QueryBuildRepoResponse{
		Items: items,
	}, nil
}

func (m *Manager) initDB(ctx context.Context) (*gorm.DB, error) {
	// if env.IsPPE() || env.IsProduct() {
	// 	db, err := m.client.InitDB(
	// 		"capcut_devops_rw:Kx6ymK9AOBkpUyB0@sd(olap.doris.capcut_devops_mysql.service.lf)/mysql?use_gdpr_auth=false&interpolateParams=true")
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// 	return db, nil
	// }
	db, err := m.client.InitDB(
		"capcut_devops_rw:Kx6ymK9AOBkpUyB0@tcp([2605:340:cd51:4301:aa48:e858:498e:6a32]:9030)/code_index_512_16")
	if err != nil {
		return nil, err
	}

	return db, nil
}

// ExistsRunningBuildRecord 根据 buildRecordParam 查询是否有正在构建的 repo 索引构建记录
func (m *Manager) existsBuildRecord(ctx context.Context, UserKnowledgeId string, status int) (bool, error) {
	db, err := m.initDB(ctx)
	if err != nil {
		return false, err
	}
	var count int64
	err = db.Table("build_record").Where("status = ? AND user_knowledge_id = ?", status, UserKnowledgeId).Count(&count).Error
	if err != nil {
		logs.CtxInfo(ctx, "query running build record failed, err: %v", err)
		return false, err
	}
	return count > 0, nil
}
