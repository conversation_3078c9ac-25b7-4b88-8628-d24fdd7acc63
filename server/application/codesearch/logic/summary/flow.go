package summary

import (
	"context"
	"fmt"
	"strings"
	"sync/atomic"
	"time"

	"code.byted.org/gopkg/jsonx"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/ies/codin/application/codesearch/entity/summary"
	"code.byted.org/ies/codin/application/codesearch/logic"
	"code.byted.org/ies/codin/application/codesearch/logic/code/entity"
	merklet "code.byted.org/ies/codin/application/codesearch/logic/merklet"
	"code.byted.org/ies/codin/application/codesearch/logic/module/filemanager"
	"code.byted.org/ies/codin/application/codesearch/logic/summary/builder"
	summaryEntity "code.byted.org/ies/codin/application/codesearch/logic/summary/entity"
	agentTools "code.byted.org/ies/codin/application/codesearch/logic/summary/tools/agent"
	"code.byted.org/ies/codin/application/codesearch/logic/summary/update"
	"code.byted.org/ies/codin/application/codesearch/logic/summary/update/debug"
	summaryUpdate "code.byted.org/ies/codin/application/codesearch/logic/summary/update/summary_update"
	knowledge "code.byted.org/ies/codin/application/codesearch/repo/knowledge"
	knowledgeManager "code.byted.org/ies/codin/application/codesearch/repo/knowledge"
	knowledgeEntity "code.byted.org/ies/codin/application/codesearch/repo/knowledge/entity"
	planEntity "code.byted.org/ies/codin/application/codesearch/repo/plan/entity"
	plan "code.byted.org/ies/codin/application/codesearch/repo/plan/service"
	metaStore "code.byted.org/ies/codin/application/codesearch/repo/summary_meta_store"
	tos "code.byted.org/ies/codin/application/codesearch/repo/tos"
	"code.byted.org/ies/codin/application/codesearch/repo/tos/config"
	"code.byted.org/ies/codin/application/codesearch/repo/workspace"
	workspaceEntity "code.byted.org/ies/codin/application/codesearch/repo/workspace/entity"
	"code.byted.org/ies/codin/application/codesearch/utils"
	"code.byted.org/ies/codin/common/group"
	"code.byted.org/overpass/capcut_devops_codesearch/kitex_gen/codesearch"
)

func GetSummary(ctx context.Context, app *logic.CodeSearch, request *codesearch.GetSummaryRequest) (*codesearch.GetSummaryResponse, error) {
	tosManager := tos.NewTosManager()
	tosManager.InitTosClientIfNeeded(config.SummaryStorageConfig)
	userKnowledgeId := summaryUpdate.GetSummaryUserKnowledgeId(&summaryUpdate.GetSummaryDataRequest{
		Uid:      request.Uid,
		RepoPath: request.RepoPath,
		Did:      request.Did,
		RepoName: request.RepoName,
		Branch:   request.Branch,
	})
	summaryData, err := summaryUpdate.GetUserSummaryData(ctx, app, userKnowledgeId)
	// data, err := tosManager.DownloadFileFromTos(ctx, knowledgeUtils.GetSummaryRepoId(request.RepoName))
	if err != nil {
		log.V2.Error().With(ctx).Str("---------------download_failed").Error(err).Str("没有找到summary").Emit()
		result := ""
		return &codesearch.GetSummaryResponse{
			Result_: &result,
			Code:    "200",
		}, nil
	}
	// knowledgeData := knowledgeEntity.Knowledge{}
	// err = json.Unmarshal(data, &knowledgeData)
	// if err != nil {
	// 	log.V2.Error().With(ctx).Str("---------------unmarshal_failed").Error(err).Emit()
	// 	return nil, err
	// }
	formattedTree := knowledge.FormatKnowledgeTree(&summaryData.Knowledge)

	return &codesearch.GetSummaryResponse{
		Result_: &formattedTree,
		Code:    "200",
	}, nil
}

// TaskProcessor 任务处理器接口，遵循依赖倒置原则
type TaskProcessor interface {
	ProcessTask(ctx context.Context, task *planEntity.TaskRecord, plan *planEntity.PlanRecord, params *TaskProcessParams) error
}

// TaskProcessParams 任务处理参数
type TaskProcessParams struct {
	Depth       int
	LogKey      string
	LogDir      string
	App         *logic.CodeSearch
	TempManager *logic.TempManager
}

// DefaultTaskProcessor 默认任务处理器实现
type DefaultTaskProcessor struct{}

// ProcessTask 处理单个任务的核心流程：analyze -> plan -> explore
func (p *DefaultTaskProcessor) ProcessTask(ctx context.Context, task *planEntity.TaskRecord, plan *planEntity.PlanRecord, params *TaskProcessParams) error {
	planRecordManager := params.TempManager.PlanRecordManager
	params.LogKey = "action"

	// 开始任务
	err := planRecordManager.StartTask(ctx, plan.PlanID, task.TaskID)
	if err != nil {
		log.V2.Error().With(ctx).Str("开始任务失败").Error(err).Str("task_id", task.TaskID).Emit()
		return err
	}

	// 1. 模块分析阶段
	isComplexModule, err := p.analyzeModule(ctx, task, params)
	if err != nil {
		log.V2.Error().With(ctx).Str("模块分析失败").Error(err).Str("task_id", task.TaskID).Emit()
		return err
	}

	// 2. 如果是复杂模块，进行规划
	if isComplexModule {
		err = p.planModule(ctx, task, params)
		if err != nil {
			log.V2.Error().With(ctx).Str("模块规划失败").Error(err).Str("task_id", task.TaskID).Emit()
			return err
		}
	}

	// 3. 探索模块
	err = p.exploreModule(ctx, task, isComplexModule, params)
	if err != nil {
		log.V2.Error().With(ctx).Str("模块探索失败").Error(err).Str("task_id", task.TaskID).Emit()
		return err
	}

	// 完成任务
	planRecordManager.CompleteTask(ctx, plan.PlanID, task.TaskID)
	log.V2.Info().With(ctx).Str("任务完成").Str("task_id", task.TaskID).Emit()

	return nil
}

// analyzeModule 分析模块复杂度
func (p *DefaultTaskProcessor) analyzeModule(ctx context.Context, task *planEntity.TaskRecord, params *TaskProcessParams) (bool, error) {
	analyzedParams := &agentTools.ModuleAnalyzedParams{
		Path:     task.TargetPath,
		RepoName: params.App.RepoManager.GetCurrentRepoName(),
	}

	logLine := "onToolstart: \n module analyzed  \n"
	utils.WriteLogToDir(params.LogKey, params.LogDir, logLine)
	utils.WriteJsonToDir(params.LogKey, params.LogDir, analyzedParams)
	log.V2.Info().With(ctx).Str("开始模块分析").Str("task_id", task.TaskID).Str("path", task.TargetPath).Emit()

	moduleAnalyzedResult, err := agentTools.ExecuteModuleAnalyzed(ctx, params.App, params.TempManager, analyzedParams, params.LogDir)
	logLine = "onToolEnd: \n module analyzed  "
	utils.WriteLogToDir(params.LogKey, params.LogDir, logLine)
	utils.WriteJsonToDir(params.LogKey, params.LogDir, moduleAnalyzedResult)
	log.V2.Info().With(ctx).Str("模块分析完成").Str("task_id", task.TaskID).Emit()
	// 默认都为复杂模块，可能存在llm没有严格返回json的情况
	isComplexModule := true
	if moduleAnalyzedResult != nil {
		isComplexModule = moduleAnalyzedResult.IsComplexModule
	}
	log.V2.Info().With(ctx).Str("模块复杂度判定").Str("task_id", task.TaskID).Str("IsComplexModule", fmt.Sprintf("%t", isComplexModule)).Emit()
	return isComplexModule, err
}

// planModule 规划复杂模块
func (p *DefaultTaskProcessor) planModule(ctx context.Context, task *planEntity.TaskRecord, params *TaskProcessParams) error {
	planningParams := &agentTools.SummaryPlanningParams{
		Path:     task.TargetPath,
		Depth:    params.Depth,
		RepoName: params.App.RepoManager.GetCurrentRepoName(),
	}

	logLine := "onToolStart: \n summary planning \n  "
	utils.WriteLogToDir(params.LogKey, params.LogDir, logLine)
	utils.WriteJsonToDir(params.LogKey, params.LogDir, planningParams)
	log.V2.Info().With(ctx).Str("开始摘要规划").Str("task_id", task.TaskID).Emit()

	summaryStr, err := agentTools.ExecuteSummaryPlanning(ctx, params.App, params.TempManager, planningParams, params.LogDir)
	logLine = "onToolEnd: \n summary planning  "
	utils.WriteLogToDir(params.LogKey, params.LogDir, logLine)
	utils.WriteLogToDir(params.LogKey, params.LogDir, summaryStr)
	log.V2.Info().With(ctx).Str("摘要规划完成").Str("task_id", task.TaskID).Emit()

	return err
}

// exploreModule 探索模块
func (p *DefaultTaskProcessor) exploreModule(ctx context.Context, task *planEntity.TaskRecord, isComplexModule bool, params *TaskProcessParams) error {
	exploreParams := &agentTools.SummaryExploreParams{
		Path:            task.TargetPath,
		Depth:           params.Depth,
		IsComplexModule: isComplexModule,
		Task:            task.Definition,
		RepoName:        params.App.RepoManager.GetCurrentRepoName(),
	}

	logLine := "onToolStart: \n summary explore \n  "
	utils.WriteJsonToDir(params.LogKey, params.LogDir, exploreParams)
	log.V2.Info().With(ctx).Str("开始摘要探索").Str("task_id", task.TaskID).Emit()

	summaryExploreResult, err := agentTools.ExecuteSummaryExplore(ctx, params.App, params.TempManager, exploreParams, params.LogDir)
	logLine = "onToolEnd: \n summary explore  "
	utils.WriteLogToDir(params.LogKey, params.LogDir, logLine)
	utils.WriteLogToDir(params.LogKey, params.LogDir, summaryExploreResult)
	log.V2.Info().With(ctx).Str("摘要探索完成").Str("task_id", task.TaskID).Emit()

	return err
}

// PlanExecutor 计划执行器，负责管理计划的执行流程
type PlanExecutor struct {
	taskProcessor TaskProcessor
	maxWorkers    int
}

// NewPlanExecutor 创建计划执行器
func NewPlanExecutor(taskProcessor TaskProcessor, maxWorkers int) *PlanExecutor {
	return &PlanExecutor{
		taskProcessor: taskProcessor,
		maxWorkers:    maxWorkers,
	}
}

// ExecutePlans 执行所有计划的公共流程
func (e *PlanExecutor) ExecutePlans(ctx context.Context, planRecordManager *plan.PlanRecordManager, params *TaskProcessParams) error {
	// 初始化通道
	taskChan := make(chan *TaskWithPlan, e.maxWorkers)
	resultChan := make(chan struct{}, e.maxWorkers)
	stopChan := make(chan struct{})

	// 启动worker协程
	e.startWorkers(ctx, taskChan, resultChan, stopChan, params)

	// 执行计划调度
	err := e.schedulePlans(ctx, planRecordManager, taskChan, resultChan, params)

	// 停止所有worker
	close(stopChan)
	log.V2.Info().With(ctx).Str("已发送停止信号给所有worker").Emit()

	return err
}

// startWorkers 启动worker协程
func (e *PlanExecutor) startWorkers(ctx context.Context, taskChan chan *TaskWithPlan, resultChan chan struct{}, stopChan chan struct{}, params *TaskProcessParams) {
	log.V2.Info().With(ctx).Str("--------------------启动worker协程-----------------").Str("workerCount", fmt.Sprintf("%d", e.maxWorkers)).Emit()

	handlers := make([]func() error, 0)
	for i := 0; i < e.maxWorkers; i++ {
		workerID := i
		handlers = append(handlers, func() error {
			log.V2.Info().With(ctx).Str("worker启动").Str("workerID", fmt.Sprintf("%d", workerID)).Emit()
			for {
				select {
				case taskWithPlan := <-taskChan:
					log.V2.Debug().With(ctx).Str("worker开始处理任务").Str("workerID", fmt.Sprintf("%d", workerID)).Str("plan_id", taskWithPlan.Plan.PlanID).Str("task_id", taskWithPlan.Task.TaskID).Str("target_path", taskWithPlan.Task.TargetPath).Emit()

					err := e.taskProcessor.ProcessTask(ctx, taskWithPlan.Task, taskWithPlan.Plan, params)
					if err != nil {
						log.V2.Error().With(ctx).Str("任务处理失败").Error(err).Str("task_id", taskWithPlan.Task.TaskID).Emit()
					}

					resultChan <- struct{}{}

				case <-stopChan:
					log.V2.Info().With(ctx).Str("worker收到停止信号").Str("workerID", fmt.Sprintf("%d", workerID)).Emit()
					return nil
				}
			}
		})
	}
	// 使用 group.Go 启动所有 worker
	group.Go(ctx, 0, func(ctx context.Context) {
		group.GoAndWait(handlers...)
	})
}

// schedulePlans 调度计划执行
func (e *PlanExecutor) schedulePlans(ctx context.Context, planRecordManager *plan.PlanRecordManager, taskChan chan *TaskWithPlan, resultChan chan struct{}, params *TaskProcessParams) error {
	// 初始化计划队列
	planQueue, planTaskKeys, completedPlans := e.initializePlanQueue(ctx, planRecordManager)

	log.V2.Info().With(ctx).Str("计划队列初始化完成").Str("plan_count", fmt.Sprintf("%d", len(planQueue))).Emit()

	// 全局任务调度循环
	activeTasks := 0
	for len(planQueue) > 0 || activeTasks > 0 {
		// 动态检查新计划
		e.checkNewPlans(ctx, planRecordManager, &planQueue, &planTaskKeys, completedPlans)

		// 分配任务
		activeTasks = e.assignTasks(ctx, planRecordManager, &planQueue, &planTaskKeys, completedPlans, taskChan, activeTasks)

		// 等待任务完成
		select {
		case <-resultChan:
			activeTasks--
			log.V2.Info().With(ctx).Str("收到任务完成信号").Str("active_tasks", fmt.Sprintf("%d", activeTasks)).Emit()
			e.checkCompletedPlans(ctx, planRecordManager, &planQueue, &planTaskKeys, completedPlans)
		case <-time.After(1000 * time.Millisecond):
			// 超时，继续循环
		}
	}

	return nil
}

// initializePlanQueue 初始化计划队列
func (e *PlanExecutor) initializePlanQueue(ctx context.Context, planRecordManager *plan.PlanRecordManager) ([]*planEntity.PlanRecord, map[string][]string, map[string]bool) {
	completedPlans := make(map[string]bool)
	planQueue := make([]*planEntity.PlanRecord, 0)
	planTaskKeys := make(map[string][]string)

	log.V2.Info().With(ctx).Str("开始初始化计划队列").Emit()

	for {
		plan, err := planRecordManager.GetNextPlan(ctx)
		if err != nil {
			log.V2.Info().With(ctx).Str("没有更多计划需要处理").Error(err).Emit()
			break
		}

		log.V2.Info().With(ctx).Str("成功获取到计划").Str("plan_id", plan.PlanID).Str("plan_state", string(plan.State)).Emit()

		if !completedPlans[plan.PlanID] {
			planQueue = append(planQueue, plan)
			var taskKeys []string
			for taskKey := range plan.Tasks {
				taskKeys = append(taskKeys, taskKey)
			}
			planTaskKeys[plan.PlanID] = taskKeys
			planRecordManager.UpdatePlanState(ctx, plan.PlanID, planEntity.PlanPhasePreparing)
			log.V2.Info().With(ctx).Str("发现新计划").Str("plan_id", plan.PlanID).Str("task_count", fmt.Sprintf("%d", len(taskKeys))).Str("total_plans", fmt.Sprintf("%d", len(planQueue))).Emit()
		} else {
			log.V2.Info().With(ctx).Str("计划已完成，跳过").Str("plan_id", plan.PlanID).Emit()
		}
	}

	return planQueue, planTaskKeys, completedPlans
}

// checkNewPlans 检查新计划
func (e *PlanExecutor) checkNewPlans(ctx context.Context, planRecordManager *plan.PlanRecordManager, planQueue *[]*planEntity.PlanRecord, planTaskKeys *map[string][]string, completedPlans map[string]bool) {
	for {
		plan, err := planRecordManager.GetNextPlan(ctx)
		if err != nil {
			break
		}
		if !completedPlans[plan.PlanID] && !isPlanInQueue(plan.PlanID, *planQueue) {
			*planQueue = append(*planQueue, plan)
			var taskKeys []string
			for taskKey := range plan.Tasks {
				taskKeys = append(taskKeys, taskKey)
			}
			(*planTaskKeys)[plan.PlanID] = taskKeys
			planRecordManager.UpdatePlanState(ctx, plan.PlanID, planEntity.PlanPhasePreparing)
			log.V2.Info().With(ctx).Str("动态发现新计划").Str("plan_id", plan.PlanID).Str("task_count", fmt.Sprintf("%d", len(taskKeys))).Str("total_plans", fmt.Sprintf("%d", len(*planQueue))).Emit()
		}
	}
}

// assignTasks 分配任务
func (e *PlanExecutor) assignTasks(ctx context.Context, planRecordManager *plan.PlanRecordManager, planQueue *[]*planEntity.PlanRecord, planTaskKeys *map[string][]string, completedPlans map[string]bool, taskChan chan *TaskWithPlan, activeTasks int) int {
	for i := 0; i < len(*planQueue); i++ {
		plan := (*planQueue)[i]

		if activeTasks >= e.maxWorkers {
			log.V2.Debug().With(ctx).Str("worker已满，无法分配新任务").Str("active_tasks", fmt.Sprintf("%d", activeTasks)).Str("max_concurrent", fmt.Sprintf("%d", e.maxWorkers)).Emit()
			break
		}

		taskKeys := (*planTaskKeys)[plan.PlanID]
		if len(taskKeys) == 0 {
			if e.isPlanCompleted(ctx, planRecordManager, plan) {
				planRecordManager.CompletePlan(ctx, plan.PlanID)
				completedPlans[plan.PlanID] = true
				*planQueue = append((*planQueue)[:i], (*planQueue)[i+1:]...)
				delete(*planTaskKeys, plan.PlanID)
				i--
				log.V2.Info().With(ctx).Str("计划完成").Str("plan_id", plan.PlanID).Emit()
			}
			continue
		}

		taskKey := taskKeys[0]
		task := plan.Tasks[taskKey]
		(*planTaskKeys)[plan.PlanID] = taskKeys[1:]

		if task.TaskStatus == planEntity.TaskStatusCompleted {
			log.V2.Debug().With(ctx).Str("任务已完成，跳过").Str("task_id", task.TaskID).Emit()
			if e.isPlanCompleted(ctx, planRecordManager, plan) {
				err := planRecordManager.CompletePlan(ctx, plan.PlanID)
				if err != nil {
					log.V2.Error().With(ctx).Str("完成计划失败").Error(err).Str("plan_id", plan.PlanID).Emit()
				} else {
					log.V2.Info().With(ctx).Str("计划已完成").Str("plan_id", plan.PlanID).Emit()
					completedPlans[plan.PlanID] = true
					*planQueue = append((*planQueue)[:i], (*planQueue)[i+1:]...)
					delete(*planTaskKeys, plan.PlanID)
					i--
				}
			}
			continue
		}

		select {
		case taskChan <- &TaskWithPlan{Task: &task, Plan: plan}:
			activeTasks++
			if plan.State == planEntity.PlanPhasePreparing {
				planRecordManager.UpdatePlanState(ctx, plan.PlanID, planEntity.PlanPhaseExecuting)
				log.V2.Info().With(ctx).Str("计划状态设置为执行中").Str("plan_id", plan.PlanID).Emit()
			}
			log.V2.Info().With(ctx).Str("任务已发送到worker").Str("plan_id", plan.PlanID).Str("task_id", task.TaskID).Str("active_tasks", fmt.Sprintf("%d", activeTasks)).Emit()
		default:
			log.V2.Debug().With(ctx).Str("任务通道已满，等待").Str("plan_id", plan.PlanID).Str("task_id", task.TaskID).Emit()
			break
		}
	}

	return activeTasks
}

// isPlanCompleted 检查计划是否完成
func (e *PlanExecutor) isPlanCompleted(ctx context.Context, planRecordManager *plan.PlanRecordManager, plan *planEntity.PlanRecord) bool {
	allTasksCompleted := true
	for _, planTask := range plan.Tasks {
		if planTask.TaskStatus != planEntity.TaskStatusCompleted {
			allTasksCompleted = false
			break
		}
	}
	return allTasksCompleted
}

// checkCompletedPlans 检查完成的计划
func (e *PlanExecutor) checkCompletedPlans(ctx context.Context, planRecordManager *plan.PlanRecordManager, planQueue *[]*planEntity.PlanRecord, planTaskKeys *map[string][]string, completedPlans map[string]bool) {
	for i := 0; i < len(*planQueue); i++ {
		plan := (*planQueue)[i]
		latestPlan, err := planRecordManager.GetPlan(ctx, plan.PlanID)
		if err != nil {
			log.V2.Error().With(ctx).Str("获取最新计划状态失败").Error(err).Str("plan_id", plan.PlanID).Emit()
			continue
		}

		if e.isPlanCompleted(ctx, planRecordManager, latestPlan) {
			err := planRecordManager.CompletePlan(ctx, plan.PlanID)
			if err != nil {
				log.V2.Error().With(ctx).Str("完成计划失败").Error(err).Str("plan_id", plan.PlanID).Emit()
			} else {
				log.V2.Info().With(ctx).Str("计划已完成").Str("plan_id", plan.PlanID).Emit()
				completedPlans[plan.PlanID] = true
				*planQueue = append((*planQueue)[:i], (*planQueue)[i+1:]...)
				delete(*planTaskKeys, plan.PlanID)
				i--
			}
		}
	}
}

func CreateAndUploadSummaryIfNeeded(ctx context.Context, app *logic.CodeSearch, request *summaryUpdate.GetSummaryDataRequest, repoURL string, merkleId string) (bool, error) {
	userKnowledgeId := summaryUpdate.GetSummaryUserKnowledgeId(request)
	summaryMetaStore, err := metaStore.NewImpl()
	// 如果已经有了那么就不用创建
	summaryMetaValue, err := summaryMetaStore.Get(ctx, userKnowledgeId)
	if err != nil && summaryMetaValue != nil {
		return false, err
	}

	summaryData := &summaryEntity.SummaryData{
		Knowledge: knowledgeEntity.Knowledge{
			Modules:   []knowledgeEntity.Module{},
			UpdatedAt: time.Now(),
		},
		MerkleId: merkleId,
	}
	getSummaryDataRequest := &summaryUpdate.GetSummaryDataRequest{
		Uid:      request.Uid,
		RepoPath: request.RepoPath,
		Did:      request.Did,
		RepoName: request.RepoName,
		Branch:   request.Branch,
	}
	_ = summaryUpdate.UpdateMetaStatus(ctx, summaryData.MerkleId, getSummaryDataRequest, summary.SummaryStatusBuilding)
	summaryUpdate.UploadSummaryData(ctx, summaryData, getSummaryDataRequest)
	knowledge, err := CreateSummary(ctx, app, &codesearch.CreateSummaryRequest{
		RepoName: request.RepoName,
		RepoURL:  repoURL,
		Branch:   request.Branch,
	})
	if err != nil {
		return false, err
	}
	summaryData = &summaryEntity.SummaryData{
		Knowledge: *knowledge,
		MerkleId:  merkleId,
	}
	_ = summaryUpdate.UpdateMetaStatus(ctx, summaryData.MerkleId, getSummaryDataRequest, summary.SummaryStatusSuccess)
	summaryUpdate.UploadSummaryData(ctx, summaryData, getSummaryDataRequest)
	return true, nil
}

// 创建仓库摘要
func CreateSummary(ctx context.Context, app *logic.CodeSearch, request *codesearch.CreateSummaryRequest) (*knowledgeEntity.Knowledge, error) {
	logKey := "summary_" + request.RepoName + "_" + request.Branch
	logDir := request.RepoName + "_" + request.Branch + "/summary"
	depth := 3
	app.RepoManager.SetCurrentRepoName(request.RepoName)
	log.V2.Info().With(ctx).Str("CreateSummary").Str("repoName", request.RepoName).Str("branch", request.Branch).Emit()

	// 初始化业务和workspace
	workspace, err := InitWorkspace(ctx, app, request)
	if err != nil {
		return nil, err
	}

	// 初始化临时管理器
	tempManager := InitializeTempManager(ctx, app, request, workspace)

	// 创建计划执行器
	taskProcessor := &DefaultTaskProcessor{}
	planExecutor := NewPlanExecutor(taskProcessor, 30) // 最大并发数30

	// 执行计划
	params := &TaskProcessParams{
		Depth:       depth,
		LogKey:      logKey,
		LogDir:      logDir,
		App:         app,
		TempManager: tempManager,
	}

	err = planExecutor.ExecutePlans(ctx, tempManager.PlanRecordManager, params)
	if err != nil {
		log.V2.Error().With(ctx).Str("执行计划失败").Error(err).Emit()
		return nil, err
	}

	log.V2.Debug().With(ctx).Str("---------------执行计划完成-----------------").Emit()
	knowledge := tempManager.KnowledgeManager.GetKnowledge(ctx)

	return knowledge, nil
}

// initializeBusinessAndWorkspace 初始化业务和workspace
func InitWorkspace(ctx context.Context, app *logic.CodeSearch, request *codesearch.CreateSummaryRequest) (*workspace.TempWorkspace, error) {
	workspace, err := app.WorkspaceManager.InitWorkspace(ctx, &workspaceEntity.RepoInfo{
		RepoName: request.RepoName,
		RepoURL:  request.RepoURL,
		Branch:   request.Branch,
	})
	if err != nil {
		log.V2.Error().With(ctx).Str("初始化workspace失败").Error(err).Emit()
		return nil, err
	}
	log.V2.Info().With(ctx).Str("workspace初始化成功").Str("path", workspace.Path).Emit()

	return workspace, nil
}

// initializeTempManager 初始化临时管理器
func InitializeTempManager(ctx context.Context, app *logic.CodeSearch, request *codesearch.CreateSummaryRequest, workspace *workspace.TempWorkspace) *logic.TempManager {
	tempManager := logic.NewTempManager(ctx)
	fileManager, err := InitFileManager(ctx, app, request)
	if err != nil {
		log.V2.Error().With(ctx).Str("---------------init_file_manager_failed").Error(err).Emit()
		return nil
	}
	tempManager.FileManager = fileManager
	planRecordManager := tempManager.PlanRecordManager
	knowledgeManager := tempManager.KnowledgeManager
	namespace := strings.ReplaceAll(request.RepoName, "/", "_") + "_" + request.Branch + "_/"

	// 先初始化存储
	// 使用当前工作目录作为基础路径
	basePath := "."
	knowledgeManager.InitStorage(basePath, namespace)
	planRecordManager.Init(basePath, namespace)

	// 再创建默认计划
	createDefaultPlanIfNeeded(ctx, tempManager.PlanRecordManager, "/")

	return tempManager
}

/**
前端和后端diff
后端拿到summary对应的merkle的版本，然后去拉对应的merkle树做diff

前端触发summary更新
1、先触发diff
提交当前的merkle树给后台，后台拿到merkle树，和当前的summary所对应的merkle树做比对，会得到一波diff
diff去knowledge上找关联的上下游模块，然后返回给前端需要后续提交的文件
- 如果变更文件父级是module,并且还是其他模块的subModule，那么提取出当前module这一层的所有文件
	- 重新分析模块，更新module的summary和keyFeatures
	- 更新当前模块作为其他子模块的subModule的摘要
- 如果变更文件父级是subModule不是module，那么说明当前是叶子节点，提取该subModule里面的所有内容，还需要提取父级module重新做summary，看keyFeatures
	- 分析子模块复杂度，更新subModule，然后确定subModule是否在变更之后足够复杂，需要新增module

2、再触发update
提交最新的merkle树、变更的文件给后台，后台开始更新summary，拿到上行的文件内容做模块的分析
- 变更文件的父级是module,并且还是其他模块的subModule
	- 拿当前所有的文件和原始summary去做摘要，更新summary、keyFeatures
	- 做一版简单摘要更新parentModule的subModule，分析是否要更新父module的keyFeatures
- 变更文件父级是subModule不是module
	- 拿到变更的文件做复杂度识别
	- 如果是复杂模块需要重新exploreModule然后新增moudule
	- 如果是简单模块，那么直接更新当前module的subModule，分析是否要更新父module的keyFeatures

更新完之后，上传新的summary到tos，summary中包括最新的merkle树id
注意这里要存多份索引key
一份是userKnowledgeId对应的summary
一份是knowledgeId和branchName对应的summary
一份是knowledgebaseId对应的summary
*/

/*
前端带着merkle树来后端查询代码变更
1、通过userId、repoName、branchName、path去下载对应的summaryData数据（也就是指定代码版本的summary）
2、然后通过summaryData中的merkleId去下载merkle树，得到当前summary版本所对应的merkle树
3、summary的merkle树和前端传来的merkle树做diff，得到diffs
4、然后通过diff去获取相关的模块路径和叶子子模块路径
- moduleDirPaths中的内容是文件目录，每次要读取目录下一层级的所有文件信息
- leafDirPaths中的内容叶子节点的目录，要递归的读取目录下所有层间的所有文件信息
5、然后把这个信息返回给前端
*/
func GetSummaryUpdateFiles(ctx context.Context, app *logic.CodeSearch, request *codesearch.GetSummaryUpdateFilesRequest) (*codesearch.GetSummaryUpdateFilesResponse, error) {
	log.V2.Info().With(ctx).Str("[GetSummaryUpdateFiles] start ").Str("uid: ", request.Uid).Str("repoName: ", request.RepoName).Str("branch: ", request.Branch).Str("repoPath: ", request.RepoPath).Str("did: ", request.Did).Emit()
	// 使用时间戳和 query 组合作为 logKey
	requestKey := fmt.Sprintf("%s_%s_%s_%s", time.Now().Format("2006-01-02 15:04:05"), request.Uid, request.RepoName, request.Branch)
	requestKey = strings.ReplaceAll(requestKey, "/", "_")
	logDir := "get_summary_related_files"
	logParams := &entity.LogParams{
		RequestKey: requestKey,
		Dir:        logDir,
	}

	var (
		repoTree              []byte
		repoTreeErr           error
		summaryData           *summaryEntity.SummaryData
		originUserKnowledgeId string
		summaryDataErr        error
	)

	handlers := make([]func() error, 0)

	handlers = append(handlers, func() error {
		merkleTosManager := tos.NewTosManager()
		merkleTosManager.InitTosClientIfNeeded(config.MerkleStorageConfig)
		repoTree, repoTreeErr = merkleTosManager.DownloadFileFromTos(ctx, request.MerkleTreeKey)
		return repoTreeErr
	})

	handlers = append(handlers, func() error {
		summaryData, originUserKnowledgeId, summaryDataErr = summaryUpdate.GetSummaryData(ctx, app, &summaryUpdate.GetSummaryDataRequest{
			Uid:      request.Uid,
			RepoPath: request.RepoPath,
			Did:      request.Did,
			RepoName: request.RepoName,
			Branch:   request.Branch,
		})
		return summaryDataErr
	})

	err := group.GoAndWait(handlers...)

	if repoTreeErr != nil {
		log.V2.Error().With(ctx).Str("---------------download_merkle_tree_failed").Error(repoTreeErr).Emit()
		return nil, repoTreeErr
	}

	if summaryDataErr != nil {
		log.V2.Error().With(ctx).Str("获取摘要数据失败").Error(summaryDataErr).Emit()
		return nil, summaryDataErr
	}
	if err != nil {
		return nil, err
	}

	knowledgeManager := knowledgeManager.NewKnowledgeManager()

	// 检查 summaryData 是否为空
	if summaryData == nil {
		log.V2.Error().With(ctx).Str("摘要数据为空，无法进行差异分析").Emit()
		return nil, fmt.Errorf("摘要数据为空，无法进行差异分析")
	}
	knowledgeManager.UpdateKnowledge(ctx, &summaryData.Knowledge)

	// 检查 MerkleId 是否为空
	if summaryData.MerkleId == "" {
		log.V2.Error().With(ctx).Str("MerkleId为空，无法下载Merkle树").Emit()
		return nil, fmt.Errorf("MerkleId为空，无法下载Merkle树")
	}
	log.V2.Info().With(ctx).Str("get summary data success").Str("server merkleId: ", summaryData.MerkleId).Emit()

	serverTree, err := summaryUpdate.DownloadMerkleTree(ctx, app, summaryData.MerkleId)
	if err != nil {
		log.V2.Error().With(ctx).Str("---------------get summary merkle tree failed").Error(err).Emit()
		return nil, err
	}
	// 存储debug信息
	// if env.IsPPE() {
	debug.BuildGetRelatedFileInfoContext(ctx, summaryData, request, serverTree, logParams)
	// }
	log.V2.Info().With(ctx).Str("download merkle tree success").Emit()
	clientTree, err := update.AnalysisMerkleTree(ctx, repoTree)
	if err != nil {
		log.V2.Error().With(ctx).Str("---------------summary_marshal_failed").Error(err).Emit()
		return nil, err
	}
	diffs := merklet.DiffTrees(serverTree, clientTree)
	if len(diffs) == 0 {
		log.V2.Info().With(ctx).Str("未发现差异").Emit()
	} else {
		log.V2.Info().With(ctx).Str(fmt.Sprintf("发现 %d 个差异", len(diffs))).Emit()
	}

	modifyFilePaths := []string{}
	deleteFilePaths := []string{}
	relatedFilePaths := summaryEntity.GroupedRelatedPathInfo{}
	for _, diff := range diffs {
		if diff.Type == "delete" {
			deleteFilePaths = append(deleteFilePaths, diff.Path)
		}
		modifyFilePaths = append(modifyFilePaths, diff.Path)
	}
	// 删除文件需要做特化处理，因为删除可能直接删除目录，删除目录的情况下，要把目录也当做变更处理
	deletedDirs := update.DetectDeletedDirectories(clientTree, deleteFilePaths)
	for _, dir := range deletedDirs {
		modifyFilePaths = append(modifyFilePaths, dir)
	}
	tempRelatedFilePaths := update.GetRelatedFileList(ctx, knowledgeManager, &modifyFilePaths)
	relatedFilePaths.LeafGroups = append(relatedFilePaths.LeafGroups, tempRelatedFilePaths.LeafGroups...)
	relatedFilePaths.ModuleGroups = append(relatedFilePaths.ModuleGroups, tempRelatedFilePaths.ModuleGroups...)
	log.V2.Info().With(ctx).Str("tempRelatedFilePaths.LeafGroups: ").Int(len(tempRelatedFilePaths.LeafGroups)).Emit()
	log.V2.Info().With(ctx).Str("tempRelatedFilePaths.ModuleGroups: ").Int(len(tempRelatedFilePaths.ModuleGroups)).Emit()
	groupedRelatedPathInfo := builder.ConvertToGroupedRelatedPathInfo(&relatedFilePaths)
	log.V2.Info().With(ctx).Str("groupedRelatedPathInfo.LeafGroups: ").Int(len(groupedRelatedPathInfo.LeafGroups)).Emit()
	log.V2.Info().With(ctx).Str("groupedRelatedPathInfo.ModuleGroups: ").Int(len(groupedRelatedPathInfo.ModuleGroups)).Emit()

	var diffResults []*codesearch.DiffResult_
	for _, diff := range diffs {
		diffResults = append(diffResults, &codesearch.DiffResult_{
			Path: diff.Path,
			Type: string(diff.Type),
			Hash: diff.Hash,
		})
	}
	response := &codesearch.GetSummaryUpdateFilesResponse{
		GroupedRelatedPathInfo: groupedRelatedPathInfo,
		OriginUserKnowledgeId:  originUserKnowledgeId,
		Code:                   "200",
		Diffs:                  diffResults,
	}
	return response, nil
}

/*
前端返回模块的全部文件内容，然后后端做summary更新
1、先上传前端传来的merkle树
2、然后下载summaryData
3、通过summary中的数据还原knowledge，通过前端的merkle树和前端的groupedRelatedFileInfo还原虚拟文件系统
4、然后根据groupedRelatedFileInfo开始分module，更新summary
这里有两块，一块是module，一块是leaf，我们不区分删除和变更，统一都当做变更处理
  - 对于module，我们每次拿到module到subModule中间这一层的全量信息，所以每次是module整体还原分析summary，然后基于原始的summary和变更的文件做融合分析，更新summary、keyFeatures、subModule
    我们不需要关注删除的情况，删除也是当做代码变更，因为每次不只带增量，每次都会带上全量的文件，所以删除的情况下，自然就会少了那个被删除的文件，然后重新分析
  - 对于叶子节点，我们每次会拿到叶子模块路径下面的全部的所有文件路径和文件内容，然后做分析，更新模块的summary、keyFeatures、subModule
  - 在分析流程，我们会分析这次的全量模块文件，是否足够拆分出另外的独立的子模块，如果可以拆出子模块，则会继续分析（处理一个超大的代码变更，改动1万个文件，那么我们就会拆分成一个独立的模块）
    （如果复杂度超过阈值，那么就新增一个module，如果没那么复杂就更新一下原有的Module的summary）

5、分析结束之后，把当前最新的summary带上merkleId更新到云端

注意：ProcessCodeChangesAndAnalyzeModules 是长耗时任务，现在改为异步执行，请求会立即返回。
状态说明：
- Status "0": 更新中
- Status "1": 更新完成
- Status "2": 更新失败
*/
func UpdateSummary(ctx context.Context, app *logic.CodeSearch, request *codesearch.UpdateSummaryRequest) (*codesearch.UpdateSummaryResponse, error) {
	log.V2.Info().With(ctx).Str("[UpdateSummary] start ").Str("uid: ", request.Uid).Str("repoName: ", request.RepoName).Str("branch: ", request.Branch).Str("repoPath: ", request.RepoPath).Str("did: ", request.Did).Str("originUserKnowledgeId: ", request.OriginUserKnowledgeId).Emit()

	summaryMetaStore, err := metaStore.NewImpl()
	if err != nil {
		logs.CtxError(ctx, "UpdateSummary, new_summary_meta_store_failed, err = %v", err)
		return nil, err
	}

	summaryMeta, err := summaryMetaStore.Get(ctx, request.OriginUserKnowledgeId)
	if err != nil {
		log.V2.Error().With(ctx).Str("---------------get_summary_meta_failed").Error(err).Emit()
		return nil, err
	}

	// 当前明确状态在构建中，会直接返回，否则往下走，因此即便存储里没有 meta 依然不影响整体链路
	if summaryMeta.Status == summary.SummaryStatusBuilding {
		log.V2.Error().With(ctx).Str("---------------summary_is_building").Emit()
		return &codesearch.UpdateSummaryResponse{
			UserKnowledgeId: request.OriginUserKnowledgeId,
			Code:            "200",
		}, nil
	}

	requestKey := fmt.Sprintf("%s_%s_%s_%s", time.Now().Format("2006-01-02 15:04:05"), request.Uid, request.RepoName, request.Branch)
	requestKey = strings.ReplaceAll(requestKey, "/", "_")
	logDir := "update_summary"
	logParams := &entity.LogParams{
		RequestKey: requestKey,
		Dir:        logDir,
	}
	app.RepoManager.SetCurrentRepoName(request.RepoName)
	// 并发下载 summaryData、repoTree、groupedRelatedFileInfoBytes
	var (
		summaryData                 *summaryEntity.SummaryData
		summaryDataErr              error
		repoTree                    []byte
		repoTreeErr                 error
		groupedRelatedFileInfoBytes []byte
		groupedRelatedFileInfoErr   error
	)

	handlers := make([]func() error, 0)

	// OriginUserKnowledgeId：指的是此前基础版本的知识
	handlers = append(handlers, func() error {
		summaryData, summaryDataErr = summaryUpdate.GetUserSummaryData(ctx, app, request.OriginUserKnowledgeId)
		return summaryDataErr
	})

	handlers = append(handlers, func() error {
		merkleTosManager := tos.NewTosManager()
		merkleTosManager.InitTosClientIfNeeded(config.MerkleStorageConfig)
		repoTree, repoTreeErr = merkleTosManager.DownloadFileFromTos(ctx, request.MerkleTreeKey)
		if repoTreeErr != nil {
			log.V2.Error().With(ctx).Str("---------------download_merkle_tree_failed").Error(repoTreeErr).Emit()
			return repoTreeErr
		}
		return repoTreeErr
	})

	if request.GroupedRelatedFileInfoKey != nil {
		handlers = append(handlers, func() error {
			summaryTosManager := tos.NewTosManager()
			summaryTosManager.InitTosClientIfNeeded(config.SummaryStorageConfig)
			groupedRelatedFileInfoBytes, groupedRelatedFileInfoErr = summaryTosManager.DownloadFileFromTos(ctx, *request.GroupedRelatedFileInfoKey)
			if groupedRelatedFileInfoErr != nil {
				log.V2.Error().With(ctx).Str("---------------download_grouped_related_file_info_failed").Error(groupedRelatedFileInfoErr).Emit()
				return groupedRelatedFileInfoErr
			}
			return groupedRelatedFileInfoErr
		})
	}

	if err := group.GoAndWait(handlers...); err != nil {
		return nil, err
	}

	if summaryDataErr != nil {
		log.V2.Error().With(ctx).Str("---------------get_summary_data_failed").Error(summaryDataErr).Emit()
		return nil, summaryDataErr
	}

	log.V2.Info().With(ctx).Str("get summary data success").Str("merkleId: ", summaryData.MerkleId).Str("summaryMeta: ", jsonx.ToString(summaryMeta)).Emit()

	if repoTreeErr != nil {
		log.V2.Error().With(ctx).Str("---------------download_merkle_tree_failed").Error(repoTreeErr).Emit()
		return nil, repoTreeErr
	}

	if groupedRelatedFileInfoErr != nil {
		log.V2.Error().With(ctx).Str("---------------download_grouped_related_file_info_failed").Error(groupedRelatedFileInfoErr).Emit()
		return nil, groupedRelatedFileInfoErr
	}

	// 存储debug信息
	// if env.IsPPE() {
	debug.BuildUpdateSummaryContext(ctx, summaryData, request, logParams)
	// }

	groupedRelatedFileInfo, err := builder.ParseGroupedRelatedFileInfo(&groupedRelatedFileInfoBytes)
	if err != nil {
		log.V2.Error().With(ctx).Str("UpdateSummary, ---------------summary_marshal_failed").Error(err).Emit()
		debug.BuildUpdateSummaryContext(ctx, summaryData, request, logParams)
		return nil, err
	}
	log.V2.Info().With(ctx).Str("parse grouped related file info success").Emit()

	tosManager := tos.NewTosManager()
	tosManager.InitTosClientIfNeeded(config.MerkleStorageConfig)
	tempManager, err := InitTempManager(ctx, app, summaryData, repoTree, groupedRelatedFileInfo)
	if err != nil {
		log.V2.Error().With(ctx).Str("---------------init_temp_manager_failed").Error(err).Emit()
		debug.BuildUpdateSummaryContext(ctx, summaryData, request, logParams)
		return nil, err
	}
	log.V2.Info().With(ctx).Str("upload merkle tree success").Str("merkleId: ", request.RootMerkleId).Emit()

	// 更新summary状态为更新中
	getSummaryDataRequest := &summaryUpdate.GetSummaryDataRequest{
		Uid:      request.Uid,
		RepoPath: request.RepoPath,
		Did:      request.Did,
		RepoName: request.RepoName,
		Branch:   request.Branch,
	}
	summaryData.MerkleId = request.RootMerkleId
	_ = summaryUpdate.UpdateMetaStatus(ctx, summaryData.MerkleId, getSummaryDataRequest, summary.SummaryStatusBuilding)

	log.V2.Info().With(ctx).Str("update summary status building").Emit()
	// 剔除空模块，避免处理大量不必要的空文件夹
	groupedRelatedFileInfo = update.FilterEmptyModules(ctx, tempManager.KnowledgeManager, groupedRelatedFileInfo)

	newCtx := group.BuildAsyncCtx(ctx)
	// 异步执行summary分析更新，设置300分钟超时
	group.Go(newCtx, 300*time.Minute, func(ctx context.Context) {
		err := ProcessCodeChangesAndAnalyzeModules(ctx, app, tempManager, groupedRelatedFileInfo, logParams, 30)
		if err != nil {
			log.V2.Error().With(ctx).Str("---------------update_summary_failed").Error(err).Emit()
			_ = summaryUpdate.UpdateMetaStatus(ctx, summaryData.MerkleId, getSummaryDataRequest, summary.SummaryStatusFailed)
			return
		}

		// 更新成功，保存最新的knowledge并设置状态为完成
		summaryData.Knowledge = *tempManager.KnowledgeManager.GetKnowledge(ctx)
		_ = summaryUpdate.UploadSummaryData(ctx, summaryData, getSummaryDataRequest)
		_ = summaryUpdate.UpdateMetaStatus(ctx, summaryData.MerkleId, getSummaryDataRequest, summary.SummaryStatusSuccess)
		log.V2.Info().With(ctx).Str("异步summary更新完成").Emit()
	})

	// 立即返回响应，不等待异步任务完成
	return &codesearch.UpdateSummaryResponse{
		UserKnowledgeId: request.OriginUserKnowledgeId,
		Code:            "200",
	}, nil
}

func QuerySummaryBuildRecord(ctx context.Context, app *logic.CodeSearch, request *codesearch.QuerySummaryBuildRecordRequest) (*codesearch.QuerySummaryBuildRecordResponse, error) {
	summaryMetaStore, err := metaStore.NewImpl()
	if err != nil {
		logs.CtxError(ctx, "QuerySummaryBuildRecord, new_summary_meta_store_failed, err = %v", err)
		return nil, err
	}

	log.V2.Info().With(ctx).Str("[QuerySummaryBuildRecord] start ").Str("uid: ", request.Uid).Str("repoName: ", request.RepoName).Str("branch: ", request.Branch).Str("repoPath: ", request.RepoPath).Str("did: ", request.Did).Emit()
	userKnowledgeId := summaryUpdate.GetSummaryUserKnowledgeId(&summaryUpdate.GetSummaryDataRequest{
		Uid:      request.Uid,
		RepoPath: request.RepoPath,
		Did:      request.Did,
		RepoName: request.RepoName,
		Branch:   request.Branch,
	})
	// 处理knowledge的更新情况
	summaryData, err := summaryUpdate.GetUserSummaryData(ctx, app, userKnowledgeId)
	if err != nil {
		log.V2.Warn().With(ctx).Str("---------------get_update_summary_failed").Error(err).Emit()
		// 没找到，直接返回空给前端，前端发现没有merkleId，就会触发后续的copyFrom
		response := &codesearch.QuerySummaryBuildRecordResponse{
			RootMerkleId: "",
			Code:         "200",
		}
		return response, nil
	}

	summaryMeta, err := summaryMetaStore.Get(ctx, userKnowledgeId)
	if err != nil {
		logs.CtxError(ctx, "QuerySummaryBuildRecord, get_summary_meta_failed, err = %v", err)
		return nil, err
	}

	log.V2.Info().With(ctx).Str("get summary data success").Str("merkleId: ", summaryData.MerkleId).Emit()
	response := &codesearch.QuerySummaryBuildRecordResponse{
		RootMerkleId: summaryData.MerkleId,
		BuildStatus:  summaryMeta.Status,
		Code:         "200",
	}
	return response, nil
}

/**
 * ProcessCodeChangesAndAnalyzeModules 处理代码变更并分析模块
 * 使用并发执行提升性能，支持控制并发数
 * @param ctx - 上下文
 * @param app - 代码搜索应用实例
 * @param tempManager - 临时管理器
 * @param groupedRelatedFileInfo - 分组的相关文件信息
 * @param maxConcurrency - 最大并发数，默认为5
 * @returns error - 错误信息
 */
func ProcessCodeChangesAndAnalyzeModules(ctx context.Context, app *logic.CodeSearch, tempManager *logic.TempManager, groupedRelatedFileInfo *summaryEntity.GroupedRelatedFileInfo, logParams *entity.LogParams, maxConcurrency int) error {
	log.V2.Info().With(ctx).Str("---------------ProcessCodeChangesAndAnalyzeModules start").Emit()

	// 设置默认并发数
	if maxConcurrency <= 0 {
		maxConcurrency = 5
	}

	// 创建信号量控制并发数
	semaphore := make(chan struct{}, maxConcurrency)

	// 之前模块a 里面有[b,c,d]三个subModule，现在可能新增了一个e目录，那么要看看是否要新增加一个e的submodule
	// 也可以能是直接修改了模块a里面的代码，所以需要更新module的summary、keyFeatures、subModule
	handlers := make([]func() error, 0)
	log.V2.Info().With(ctx).Str("--------start analyze module-------\n").Emit()
	log.V2.Info().With(ctx).Str("ModuleGroups size: ").Int(len(groupedRelatedFileInfo.ModuleGroups)).Emit()
	log.V2.Info().With(ctx).Str("LeafGroups size: ").Int(len(groupedRelatedFileInfo.LeafGroups)).Emit()

	// 进度跟踪变量
	var completedCount int32
	var activeCount int32
	totalHandlers := len(groupedRelatedFileInfo.ModuleGroups) + len(groupedRelatedFileInfo.LeafGroups)

	log.V2.Info().With(ctx).Str("进度初始化").Str("总任务数", fmt.Sprintf("%d", totalHandlers)).Str("最大并发数", fmt.Sprintf("%d", maxConcurrency)).Emit()

	// 收集模块组处理任务
	for _, moduleGroup := range groupedRelatedFileInfo.ModuleGroups {
		group := moduleGroup // 创建副本避免闭包问题
		handlers = append(handlers, func() error {
			// 获取信号量
			semaphore <- struct{}{}
			atomic.AddInt32(&activeCount, 1)
			defer func() {
				<-semaphore
				atomic.AddInt32(&activeCount, -1)
				atomic.AddInt32(&completedCount, 1)
			}()

			log.V2.Info().With(ctx).Str("开始处理模块组").Str("group_path", group.GroupPath).Str("当前进度", fmt.Sprintf("%d/%d", atomic.LoadInt32(&completedCount)+1, totalHandlers)).Str("正在执行", group.GroupPath).Emit()

			module := tempManager.KnowledgeManager.GetModule(ctx, group.GroupPath)
			if module == nil {
				log.V2.Error().With(ctx).Str("-------no module found--------", group.GroupPath).Emit()
				return nil
			}
			log.V2.Info().With(ctx).Str("---------------module found").Str("module: ", module.Path).Emit()
			// 有一个前置流程根据目录树和变更的文件，判定模块归属，究竟是从属旧的module还是可以独立出一个新的module
			// 如果从属旧的模块，那么就直接ExploreModuleWithKnowledge
			// 如果是可以满足新模块的构造，那么就构造探索计划
			independentModuleAnalyzedResult := &summaryEntity.IndependentModuleAnalyzedResult{
				IsIndependentModule:   false,
				IndependentModulePath: "",
			}
			if len(group.SubFileInfos) > 0 {
				result, err := update.AnalyzeIndependentModule(ctx, app, tempManager, &group, logParams)
				if err == nil {
					independentModuleAnalyzedResult = result
				}
			}
			if independentModuleAnalyzedResult.IsIndependentModule {
				log.V2.Info().With(ctx).Str("---------------create plan").Str("plan: ", independentModuleAnalyzedResult.IndependentModulePath).Emit()
				tempManager.PlanRecordManager.CreatePlan(ctx, []*planEntity.TaskRecord{
					{
						TaskID:     "CORE-0",
						Definition: "深入分析当前目录",
						TargetPath: independentModuleAnalyzedResult.IndependentModulePath,
					},
				}, module.Path)
			}
			// 这块后续可以考虑合并
			// 更新一波当前点的summary、keyFeatures、subModule
			update.ExploreModuleWithKnowledge(ctx, app, tempManager, &group, module, logParams)
			log.V2.Info().With(ctx).Str("---------------explore module success").Str("module: ", module.Path).Emit()
			parentModule := tempManager.KnowledgeManager.GetModuleBySubModulePath(ctx, group.GroupPath)
			if parentModule != nil {
				// 更新一波父节点的summary、keyFeatures、subModule
				update.ExploreModuleWithKnowledge(ctx, app, tempManager, &group, parentModule, logParams)
				log.V2.Info().With(ctx).Str("---------------explore parent module success").Str("module: ", parentModule.Path).Emit()
			}

			log.V2.Info().With(ctx).Str("完成处理模块组").Str("group_path", group.GroupPath).Str("当前进度", fmt.Sprintf("%d/%d", atomic.LoadInt32(&completedCount), totalHandlers)).Emit()
			return nil
		})
	}

	log.V2.Info().With(ctx).Str("---------------ProcessCodeChangesAndAnalyzeModules end").Emit()
	// 需要去analysed module，看看模块的复杂度，看看这个模块是否要做为新的module
	// 之前就是subModule，那么看看是否要作为一个独立的模块
	// 如果是复杂模块，那么要用plan做规划，拆分任务，然后做explore
	// 如果不是复杂模块，那么应该带着父级的module的summary分析一下，更新父级的module的summary、keyFeatures、subModule

	// 收集叶子组处理任务
	for _, leafGroup := range groupedRelatedFileInfo.LeafGroups {
		group := leafGroup // 创建副本避免闭包问题
		handlers = append(handlers, func() error {
			// 获取信号量
			semaphore <- struct{}{}
			atomic.AddInt32(&activeCount, 1)
			defer func() {
				<-semaphore
				atomic.AddInt32(&activeCount, -1)
				atomic.AddInt32(&completedCount, 1)
			}()

			log.V2.Info().With(ctx).Str("开始处理叶子组").Str("group_path", group.GroupPath).Str("当前进度", fmt.Sprintf("%d/%d", atomic.LoadInt32(&completedCount)+1, totalHandlers)).Str("正在执行", group.GroupPath).Emit()

			parentModule := tempManager.KnowledgeManager.GetModuleBySubModulePath(ctx, group.GroupPath)
			if parentModule == nil {
				// 如果叶子节点没有父module，大概率父节点已经被删除了
				log.V2.Warn().With(ctx).Str("-------Leaf: no parent module found--------", group.GroupPath).Emit()
				return nil
			}
			independentModuleAnalyzedResult := &summaryEntity.IndependentModuleAnalyzedResult{
				IsIndependentModule:   false,
				IndependentModulePath: "",
			}
			if len(group.SubFileInfos) > 0 {
				result, err := update.AnalyzeIndependentModule(ctx, app, tempManager, &group, logParams)
				if err == nil {
					independentModuleAnalyzedResult = result
				}
			}
			log.V2.Info().With(ctx).Str("---------------analyze leaf module success").Str("module: ", parentModule.Path).Emit()
			// 如果是复杂模块，那么需要重新创建独立的探索任务，然后自动探索函数的子目录子模块
			if independentModuleAnalyzedResult.IsIndependentModule {
				log.V2.Info().With(ctx).Str("---------------create plan").Str("plan: ", independentModuleAnalyzedResult.IndependentModulePath).Emit()
				// 创建探索计划
				tempManager.PlanRecordManager.CreatePlan(ctx, []*planEntity.TaskRecord{
					{
						TaskID:     "CORE-0",
						Definition: "深入分析当前目录",
						TargetPath: independentModuleAnalyzedResult.IndependentModulePath,
						TaskStatus: planEntity.TaskStatusPlanned,
						Priority:   planEntity.TaskPriorityHigh,
					},
				}, parentModule.Path)
			}
			// 如果之前就是叶子节点，且现在还是简单模块，那么只需要更新module的summary、keyFeatures、subModule
			update.ExploreModuleWithKnowledge(ctx, app, tempManager, &group, parentModule, logParams)
			log.V2.Info().With(ctx).Str("---------------explore leaf module success").Str("module: ", parentModule.Path).Emit()

			log.V2.Info().With(ctx).Str("完成处理叶子组").Str("group_path", group.GroupPath).Str("当前进度", fmt.Sprintf("%d/%d", atomic.LoadInt32(&completedCount), totalHandlers)).Emit()
			return nil
		})
	}

	log.V2.Info().With(ctx).Str("开始并发执行").Str("总任务数", fmt.Sprintf("%d", totalHandlers)).Str("最大并发数", fmt.Sprintf("%d", maxConcurrency)).Emit()

	// 并发执行所有任务
	err := group.GoAndWait(handlers...)
	if err != nil {
		log.V2.Error().With(ctx).Str("---------------并发处理中出现错误").Error(err).Emit()
		return err
	}

	log.V2.Info().With(ctx).Str("并发执行完成").Str("总任务数", fmt.Sprintf("%d", totalHandlers)).Str("完成数", fmt.Sprintf("%d", atomic.LoadInt32(&completedCount))).Emit()

	log.V2.Info().With(ctx).Str("---------------execute module exploration start").Emit()
	// 使用重构后的公共流程执行模块探索
	// todo(liboti)
	// 创建了新模块之后，要把新模块加到父级的subModule里面
	err = executeModuleExploration(ctx, app, tempManager, logParams, maxConcurrency)
	if err != nil {
		log.V2.Error().With(ctx).Str("模块探索失败").Error(err).Emit()
		return err
	}
	return nil
}

// executeModuleExploration 执行模块探索的公共流程
func executeModuleExploration(ctx context.Context, app *logic.CodeSearch, tempManager *logic.TempManager, localLogParams *entity.LogParams, maxConcurrency int) error {
	// 创建计划执行器
	taskProcessor := &DefaultTaskProcessor{}
	planExecutor := NewPlanExecutor(taskProcessor, maxConcurrency)
	logDir := "update/explore"
	logKey := "update_summary"
	// 执行计划
	params := &TaskProcessParams{
		Depth:       3,
		LogKey:      logKey,
		LogDir:      localLogParams.Dir + "/" + localLogParams.RequestKey + "/" + logDir,
		App:         app,
		TempManager: tempManager,
	}

	return planExecutor.ExecutePlans(ctx, tempManager.PlanRecordManager, params)
}

// TaskWithPlan 任务和计划的组合结构 / Task and plan combination structure
type TaskWithPlan struct {
	Task *planEntity.TaskRecord
	Plan *planEntity.PlanRecord
}

func isPlanInQueue(planID string, planQueue []*planEntity.PlanRecord) bool {
	for _, plan := range planQueue {
		if plan.PlanID == planID {
			return true
		}
	}
	return false
}

func createDefaultPlanIfNeeded(ctx context.Context, planRecordManager *plan.PlanRecordManager, path string) {
	_, err := planRecordManager.GetNextPlan(ctx)
	if err == nil {
		log.V2.Info().With(ctx).Str("---------------plan_already_exists").Emit()
		return
	}
	// 如果没有计划，创建一个默认计划
	planRecordManager.CreatePlan(ctx, []*planEntity.TaskRecord{
		{
			TaskID:     "CORE-0",
			Definition: "深入分析当前目录",
			TargetPath: path,
			TaskStatus: planEntity.TaskStatusPlanned,
			Priority:   planEntity.TaskPriorityHigh,
		},
	}, "project_root")
	log.V2.Info().With(ctx).Str("---------------plan_created").Emit()

}

func InitFileManager(ctx context.Context, app *logic.CodeSearch, request *codesearch.CreateSummaryRequest) (*filemanager.FileManager, error) {
	workspace, err := app.WorkspaceManager.InitWorkspace(ctx, &workspaceEntity.RepoInfo{
		RepoName: request.RepoName,
		RepoURL:  request.RepoURL,
		Branch:   request.Branch,
	})
	if err != nil {
		return nil, err
	}
	fileManager := filemanager.NewLocalFileManager(workspace.Path)
	return &fileManager, nil
}

func InitTempManager(ctx context.Context, app *logic.CodeSearch, summaryData *summaryEntity.SummaryData, RepoTree []byte, groupedRelatedFileInfo *summaryEntity.GroupedRelatedFileInfo) (*logic.TempManager, error) {
	tempManager := logic.NewTempManager(ctx)
	basePath := "."
	tempManager.KnowledgeManager.InitStorage(basePath, "")
	tempManager.PlanRecordManager.Init(basePath, "")

	// 还原一下knowledge
	tempManager.KnowledgeManager.UpdateKnowledge(ctx, &summaryData.Knowledge)

	// 分析merkle树
	clientTree, err := update.AnalysisMerkleTree(ctx, RepoTree)
	if err != nil {
		log.V2.Error().With(ctx).Str("---------------summary_marshal_failed").Error(err).Emit()
		return nil, err
	}

	// 初始化文件管理器 - 从clientTree构造虚拟仓库
	fileManager, err := initFileManagerFromMerkleTree(ctx, clientTree, groupedRelatedFileInfo)
	if err != nil {
		log.V2.Error().With(ctx).Str("---------------init_file_manager_failed").Error(err).Emit()
		return nil, err
	}
	tempManager.FileManager = fileManager
	return tempManager, nil
}

// initFileManagerFromMerkleTree 从Merkle树构造虚拟仓库并初始化文件管理器
func initFileManagerFromMerkleTree(ctx context.Context, clientTree *merklet.TreeNode, groupedRelatedFileInfo *summaryEntity.GroupedRelatedFileInfo) (*filemanager.FileManager, error) {
	rootPath := "/"
	virtualTree := update.GetVirtualTreeFromMerkleTree(ctx, clientTree, groupedRelatedFileInfo, []string{})
	// 创建虚拟文件管理器
	fileManager := filemanager.NewVirtualFileManager(rootPath, virtualTree)
	log.V2.Info().With(ctx).Str("虚拟文件管理器创建完成").Emit()

	return &fileManager, nil
}
