import React from 'react';
import { Menu } from 'lucide-react';
import { DropdownMenu } from '@radix-ui/themes';

interface HamburgerMenuProps {
  businessList: string[];
  selectedBusiness: string;
  isLoadingBusinessList: boolean;
  isChangingBusiness: boolean;
  onBusinessSelect: (business: string) => void;
}

export const HamburgerMenu: React.FC<HamburgerMenuProps> = ({
  businessList,
  selectedBusiness,
  isLoadingBusinessList,
  isChangingBusiness,
  onBusinessSelect,
}) => {
  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger>
        <div className="hamburger-menu-button" title="菜单">
          <Menu size={14} />
        </div>
      </DropdownMenu.Trigger>
      <DropdownMenu.Content className="hamburger-menu-content">
        <DropdownMenu.Sub>
          <DropdownMenu.SubTrigger className="hamburger-menu-text">选择业务</DropdownMenu.SubTrigger>
          <DropdownMenu.SubContent className="hamburger-menu-sub-content">
            {isLoadingBusinessList ? (
              <DropdownMenu.Item className="hamburger-menu-text" disabled>
                加载中...
              </DropdownMenu.Item>
            ) : (
              businessList.map((business) => (
                <DropdownMenu.Item
                  className="hamburger-menu-text"
                  key={business}
                  onSelect={() => onBusinessSelect(business)}
                  disabled={isChangingBusiness}
                >
                  {selectedBusiness === business ? '✓ ' : ''}
                  {isChangingBusiness && business === selectedBusiness ? '切换中...' : business}
                </DropdownMenu.Item>
              ))
            )}
          </DropdownMenu.SubContent>
        </DropdownMenu.Sub>
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  );
};
