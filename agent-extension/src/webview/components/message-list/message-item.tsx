import React, { useC<PERSON>back, useState } from 'react';
import { AnswerRenderer } from './answer-renderer';
import { Tool<PERSON>allRenderer } from './tool-call-renderer/tool-call-renderer';
import { ClientMessageType, Role } from '../../../conversation/client-message/abstract-message';
import { ClientToolMessage } from '../../../conversation/client-message/tool-message';
import { ClientStreamingMessage } from '../../../conversation/client-message/streaming-message';
import { ClientMultiPartMessage } from '../../../conversation/client-message/multi-part-message';
import { type Message } from '../../managers/conversation-manager/types';
import { QuestionRenderer } from './question-renderer';
import { ErrorRenderer } from './error-renderer';
import type { ClientErrorMessage } from '../../../conversation/client-message/error-message';
import { safeGetVscodeState } from '../../core/acquire';
import { ThumbsDown, ThumbsUp } from 'lucide-react';

interface MessageItemProps {
  message: Message;
}

type FeedbackStatus = 'none' | 'liked' | 'disliked';

export const MessageItem: React.FC<MessageItemProps> = ({ message }) => {
  const [feedback, setFeedback] = useState<FeedbackStatus>('none');
  const state = safeGetVscodeState();
  const renderer = useCallback(() => {
    if (message.type === ClientMessageType.Error) {
      return <ErrorRenderer message={message as ClientErrorMessage} />;
    }
    if (message.type === ClientMessageType.Tool) {
      return <ToolCallRenderer message={message as ClientToolMessage} />;
    }
    if (message.role === Role.User) {
      return <QuestionRenderer message={message as ClientMultiPartMessage} />;
    }
    return <AnswerRenderer message={message as ClientStreamingMessage} />;
  }, [message]);

  return (
    <div
      className="codin-msg-container"
      {...(message.roundFinish
        ? {
            'data-round-finish': true,
            'data-cid': state.currentConversationId,
          }
        : {})}
    >
      {renderer()}
      {message.roundFinish && (
        <div className="w-full flex justify-end space-x-2">
          {/* TODO: 埋点 */}
          <ThumbsUp
            size={14}
            onClick={() => setFeedback(feedback === 'liked' ? 'none' : 'liked')}
            fill={feedback === 'liked' ? 'currentColor' : 'none'}
          />
          <ThumbsDown
            size={14}
            onClick={() => setFeedback(feedback === 'disliked' ? 'none' : 'disliked')}
            fill={feedback === 'disliked' ? 'currentColor' : 'none'}
          />
        </div>
      )}
    </div>
  );
};
