import React, { useEffect, useRef, useState } from 'react';

import { debounce } from 'lodash';

import { MessageItem } from './message-item';
import type { Message } from '../../managers/conversation-manager/types';
import type { ConversationStatus } from '../../../common/services/conversation/base-conversation-service';

interface MessageListProps {
  messages: Message[];
  messagesContainerRef: React.RefObject<HTMLDivElement>;
}

export const MessageList: React.FC<MessageListProps> = ({ messages, messagesContainerRef }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [prevMessagesLength, setPrevMessagesLength] = useState(-1);
  const [isAutoScrollEnabled, setIsAutoScrollEnabled] = useState(true);
  const [hasUserScrolled, setHasUserScrolled] = useState(false);

  // 添加滚动监听逻辑到聊天容器
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    const container = messagesContainerRef!.current;
    if (!container) {
      return;
    }

    // 添加防抖处理，避免滚动事件频繁触发
    // 添加防抖处理，避免滚动事件频繁触发 (修复语法错误)
    const debouncedHandleScroll = debounce(() => {
      const { scrollTop, scrollHeight, clientHeight } = container;
      // 计算距离底部的距离（添加45px容差，避免精准定位要求）
      const distanceToBottom = scrollHeight - (scrollTop + clientHeight);
      const threshold = 45; // 45px的阈值，允许用户接近底部时仍保持自动滚动
      const isNearBottom = distanceToBottom <= threshold;

      // 更新自动滚动状态
      setIsAutoScrollEnabled(isNearBottom);

      // 记录用户是否主动滚动过
      if (scrollTop > 0) {
        setHasUserScrolled(true);
      }
    }, 100);

    // 添加滚动事件监听
    container.addEventListener('scroll', debouncedHandleScroll);

    // 清理函数：移除事件监听
    return () => container.removeEventListener('scroll', debouncedHandleScroll);
  }, []);

  console.log('MessageList', messages);

  // 自动滚动到底部 - 仅在满足条件时执行
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    // 条件1: 自动滚动功能已启用
    // 条件2: 有新消息产生（消息数量增加）
    if (isAutoScrollEnabled && messages.length > prevMessagesLength) {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
    // 更新消息数量记录
    setPrevMessagesLength(messages.length);
  }, [messages, isAutoScrollEnabled]);

  // 如果用户没有滚动过，就会一直自动滚动到底部
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>

  useEffect(() => {
    if (!hasUserScrolled) {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, hasUserScrolled]);
  return (
    <div ref={containerRef} className="flex flex-col h-full rounded-lg">
      <div className="flex-1 flex flex-col gap-3">
        {messages.map((message) => (
          <MessageItem key={message.id} message={message} />
        ))}
        <div ref={messagesEndRef} />
      </div>
    </div>
  );
};

export default MessageList;
