import { agenterrcode, type Int64 } from '@/bam';
import type { AgentId } from '../const';
import type { ConversationTypeEnum } from '@/common/constants/conversation-types';
import { MessagesManager } from './messages-manager';
import { MessageReceiver } from './message-receiver';
import { IInstantiationService } from '@byted-image/lv-bedrock/di';
import { ISocketService, type SocketClient } from '../../socket/socket-service.interface';
import { INetworkClientFactoryService } from '../../network-client-factory/network-client-factory-service.interface';
import { AskMessageRole, type AskMessage, type UserMessage } from '../../base-chat/types';
import type { ClientToolMessage } from '@/conversation/client-message/tool-message';
import { Role, type ClientMessage } from '@/conversation/client-message/abstract-message';
import { ClientMultiPartMessage } from '@/conversation/client-message/multi-part-message';
import { Disposable } from '@byted-image/lv-bedrock/dispose';
import type { ModelType } from '@/bam/namespaces/base';
import { sleep } from '@byted-image/lv-bedrock/async';
import { ClientErrorMessage } from '@/conversation/client-message/error-message';
import { ErrCode } from '@/bam/namespaces/agenterrcode';
import { IStorageService } from '../../storage/storage-service.interface';
import { IFileLoggerService } from '../../file-logger/file-logger-service.interface';

export enum RetryMode {
  /** 模型降级 */
  ModelDowngrade = 'modelDowngrade',
  /** 普通重试 */
  Normal = 'normal',
}

/** 需要重试的错误码 */
const errorCodesNeedRetry: Record<RetryMode, agenterrcode.ErrCode[]> = {
  /** 需要模型降级的错误码 */
  [RetryMode.ModelDowngrade]: [ErrCode.ServiceOverload],
  /** 普通重试错误码 */
  [RetryMode.Normal]: [ErrCode.UpdateConversationStatusFailed, ErrCode.SaveStreamFailed, ErrCode.SaveMessageFailed],
};

export abstract class BaseConversationContext extends Disposable {
  /** 业务线 */
  private _business = '';

  /** 普通重试次数 */
  private _normalRetryCount = 0;
  /** 普通重试最大次数 */
  private _maxNormalRetryCount = 2;

  /** 当前模型队列索引 */
  protected _currentModelQueueIndex = 0;

  protected abstract readonly _conversationType: ConversationTypeEnum;
  protected abstract readonly _agentType: AgentId;

  protected _messagesManager!: MessagesManager;
  protected _messageReceiver!: MessageReceiver;
  private _socket!: SocketClient;
  protected _networkClient = this._networkClientFactoryService.build({});

  /** 获取模型队列 */
  protected abstract _getModelQueue(): Promise<ModelType[]>;

  protected abstract _retryWhenMessageError(): Promise<void>;

  get messagesManager() {
    return this._messagesManager;
  }

  get messages() {
    return this._messagesManager.getMessages();
  }

  constructor(
    protected readonly _options: {
      cid: string;
      parentCid?: string;
      parentMessageVersion?: Int64;
    },
    private readonly _initMessages: ClientMessage[],
    @IInstantiationService protected readonly _instantiationService: IInstantiationService,
    @ISocketService protected readonly _socketService: ISocketService,
    @INetworkClientFactoryService protected readonly _networkClientFactoryService: INetworkClientFactoryService,
    @IStorageService protected readonly _storageService: IStorageService,
    @IFileLoggerService protected readonly _fileLoggerService: IFileLoggerService,
  ) {
    super();
  }

  public get id() {
    return this._options.cid;
  }

  public get parentId() {
    return this._options.parentCid;
  }

  public get parentMessageVersion() {
    return this._options.parentMessageVersion;
  }

  public get conversationType() {
    return this._conversationType;
  }

  public get agentType() {
    return this._agentType;
  }

  public get messageReceiver() {
    return this._messageReceiver;
  }

  protected async _getCurrentModel(): Promise<ModelType> {
    const modelQueue = await this._getModelQueue();
    this._fileLoggerService.info(
      `${this._conversationType} _getCurrentModel: queue: ${JSON.stringify(modelQueue)}, currentModelQueueIndex: ${this._currentModelQueueIndex}`,
    );
    return modelQueue[this._currentModelQueueIndex];
  }

  public async getBusiness() {
    if (this._business) {
      return this._business;
    }

    const business = (await this._storageService.get(`conversation_business_${this.id}`)) || '';
    console.log('getBusiness', this._conversationType, this.id, business, this.parentId);
    if (!business && this.parentId) {
      const parentBusiness = await this._storageService.get(`conversation_business_${this.parentId}`);
      return parentBusiness || '';
    }

    return business;
  }

  public async setBusiness(business: string) {
    this._business = business;
    await this._storageService.set(`conversation_business_${this.id}`, business);
  }

  public async bootstrap() {
    await this._createSocket();
    this._messagesManager = this._instantiationService.createInstance(
      MessagesManager,
      {
        agentType: this._agentType,
        cid: this._options.cid,
        parentCid: this._options.parentCid,
        parentMessageVersion: this._options.parentMessageVersion,
        conversationType: this._conversationType,
      },
      this._initMessages,
    );
    this._messageReceiver = this._instantiationService.createInstance(MessageReceiver, this._socket);
    this._register(
      this._messageReceiver.onMessageError(async (error) => {
        // 发生错误时保存一手消息
        this._messagesManager.autoSaveConversation();
        console.log('onMessageError', error);
        this._fileLoggerService.info(
          `${this._conversationType} onMessageError: ${JSON.stringify(error)}, retryCount: ${this._normalRetryCount}, currentModelQueueIndex: ${this._currentModelQueueIndex}`,
        );

        if (!error.errCode) {
          this._fileLoggerService.error(`${this._conversationType} onMessageError: ${JSON.stringify(error)}`);
          this._appendErrorMessage(error);
          return;
        }

        if (errorCodesNeedRetry[RetryMode.ModelDowngrade].includes(error.errCode)) {
          const modelQueue = await this._getModelQueue();
          if (this._currentModelQueueIndex >= modelQueue.length) {
            this._appendErrorMessage(error);
            return;
          }
          this._currentModelQueueIndex += 1;
          this._retryWhenMessageError();
          return;
        }

        if (errorCodesNeedRetry[RetryMode.Normal].includes(error.errCode)) {
          if (this._normalRetryCount >= this._maxNormalRetryCount) {
            this._appendErrorMessage(error);
            return;
          }
          await sleep(2000);
          this._normalRetryCount += 1;
          this._retryWhenMessageError();
          return;
        }

        this._appendErrorMessage(error);
      }),
    );

    this._register(
      this._messageReceiver.onAddMessages((ev) => {
        this._messagesManager.appendMessages(ev);
      }),
    );

    this._register(
      this._messageReceiver.onSteamingMessageReceive((clientMessage) => {
        // 只有父会话才标记
        if (clientMessage.finishReason === 'stop' && !this.parentId) {
          clientMessage.roundFinish = true;
        }
        this._messagesManager.updateMessage(clientMessage);
      }),
    );
  }

  private _appendErrorMessage(error: { errCode?: agenterrcode.ErrCode; msg?: string }) {
    this._messagesManager.appendMessages([
      new ClientErrorMessage({
        code: error.errCode ?? -1,
        message: error.msg ?? '',
        version: -1, // 错误消息没有 version
      }),
    ]);
    this._messageReceiver?.resetActiveStreamingMessage();
  }

  /**
   * 发送用户消息
   * @param message
   */
  protected sendUserMessage(message: UserMessage, logId?: string) {
    const lastMessage = this._messagesManager.getMessages().at(-1);
    const nextVersion = lastMessage
      ? typeof lastMessage.version === 'number'
        ? lastMessage.version + 1
        : Number(lastMessage.version) + 1
      : 0;
    const userMessage = new ClientMultiPartMessage({
      content: message.userContent,
      role: Role.User,
      createdAt: new Date().toISOString(),
      version: nextVersion,
      logID: logId,
    });

    this._messagesManager.appendMessages([userMessage]);
    this._messageReceiver?.resetActiveStreamingMessage();
    return userMessage;
  }

  /**
   * @param message
   * @param logId
   */
  protected _updateMessageFromLocal(message: AskMessage, logId?: string) {
    if (message.role === AskMessageRole.User) {
      this.sendUserMessage(message, logId);
    }
    // 客户端工具处理消息
    if (message.role === AskMessageRole.Tool) {
      for (const data of message.toolsData) {
        const toolMessage = this._messagesManager.getMessageById(data.requestId) as ClientToolMessage;
        if (!toolMessage) {
          continue;
        }

        toolMessage.output = data.content;
        this._messagesManager.updateMessage(toolMessage);
      }
      this._messageReceiver?.resetActiveStreamingMessage();
    }
  }

  private _createSocket() {
    this._fileLoggerService.info(`${this._conversationType} _createSocket start`);
    this._socket = this._socketService.connect({
      serviceId: 117440539,
      conversationId: this._options.cid,
    });

    return new Promise((resolve, reject) => {
      this._socket!.onOpen(() => {
        this._fileLoggerService.info(`${this._conversationType} _createSocket onOpen`);
        resolve(true);
        this._register({
          dispose: () => {
            this._socket.dispose();
          },
        });
      });

      this._socket!.onError((ev) => {
        this._fileLoggerService.error(`${this._conversationType} _createSocket onError`);
        reject(ev.error);
      });
    });
  }
}
