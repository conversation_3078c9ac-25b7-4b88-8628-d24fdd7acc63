import type { ClientMessage } from '@/conversation/client-message/abstract-message';
import { createDecorator } from '@byted-image/lv-bedrock/di';
import type { ILvErrorOr } from '@byted-image/lv-bedrock/error';
import type { Event } from '@byted-image/lv-bedrock/event';

export interface IPrivateConversationService {
  _serviceBrand: undefined;

  id: string;

  createConversation(): Promise<ILvErrorOr<string>>;
  getMessages(): ClientMessage[];
  appendMessages: (messages: ClientMessage[]) => void;
  presentAssistantMessage: () => void;
  switchConversation: (cid: string) => Promise<ILvErrorOr<void>>;
  // 展示Assistant消息事件
  onPresentAssistantMessage: Event<[]>;
  // 更新事件
  onAppendMessages: Event<[ClientMessage[]]>;
}

export const IPrivateConversationService = createDecorator<IPrivateConversationService>('private-conversation-service');
