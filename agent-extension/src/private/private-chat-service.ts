import type { AgentInferenceParams } from '@/bam/namespaces/agentserver';
import { getSystemEnv } from '@/common/env/system';
import { type AskMessage, AskMessageRole } from '@/common/services/base-chat/types';
import { IMcpHubService } from '@/common/services/mcp-hub/mcp-hub-service.interface';
import { IRulesService, RuleType } from '@/common/services/rules/rules-service.interface';
import { ClientMessageType, Role } from '@/conversation/client-message/abstract-message';
import { ClientMultiPartMessage } from '@/conversation/client-message/multi-part-message';
import type { ClientToolMessage } from '@/conversation/client-message/tool-message';
import type { Tool } from '@/tools/base';
import { ExecuteCommandTool } from '@/tools/execute-command';
import { FetchRuleTool } from '@/tools/fetch-rule';
import { FigmaComponentDetailTool } from '@/tools/figma-component-detail';
import { GetAllErrorsTool } from '@/tools/get-all-errors';
import { GetErrorsTool } from '@/tools/get-errors';
import { ListDirectoryTool } from '@/tools/list-directory';
import { ReadFileTool } from '@/tools/read-file';
import { ReplaceFileTool } from '@/tools/replace-file';
import { WriteFileTool } from '@/tools/write-file';
import { IInstantiationService } from '@byted-image/lv-bedrock/di';
import { Emitter, type Event } from '@byted-image/lv-bedrock/event';
import { IPrivateChatService } from './private-chat-service.interface';
import { IPrivateConversationService } from './private-conversation-service.interface';
import { SSEReceiver } from './sse-receiver';

export class PrivateChatService implements IPrivateChatService {
  public _serviceBrand: undefined;

  public onUpdate: Event<[]>;
  public onPresentAssistantMessage: Event<[]>;

  private readonly _onUpdate = new Emitter<[]>();
  private readonly _onPresentAssistantMessage = new Emitter<[]>();

  private _messageReceiver: SSEReceiver | null = null;

  constructor(
    @IPrivateConversationService
    private readonly _conversationService: IPrivateConversationService,
    @IInstantiationService private readonly _instantiationService: IInstantiationService,
    @IRulesService private readonly _rulesService: IRulesService,
    @IMcpHubService private readonly _mcpHubService: IMcpHubService,
  ) {
    this.onUpdate = this._onUpdate.event;
    this.onPresentAssistantMessage = this._onPresentAssistantMessage.event;

    this._conversationService.onPresentAssistantMessage(() => {
      this._onPresentAssistantMessage.fire();
    });

    this._conversationService.onAppendMessages((messages) => {
      const toolMessages = messages.filter((message) => message.type === ClientMessageType.Tool);
      this._handleToolMessages(toolMessages as ClientToolMessage[]);
    });
  }

  public getMessages() {
    return this._conversationService.getMessages();
  }

  public async sendMessage(message: AskMessage) {
    if (!this._messageReceiver) {
      this._createMessageReceiver();
    }
    if (!this._conversationService.id) {
      const res = await this._conversationService.createConversation();
      if (!res.ok) {
        console.error(res.toString());
        return;
      }
    }
    this._updateMessage(message);
    const alwaysRulesContent = await this._rulesService.getRulesContentByType(RuleType.Always);
    const params = {
      id: this._conversationService.id,
      client_env: {
        system_env: getSystemEnv(),
        tools: this._getTools()
          .filter((tool) => tool.enable())
          .map((tool) => tool.getInfo()),
        project_rules: alwaysRulesContent,
      },
    } as AgentInferenceParams;
    if (message.role === AskMessageRole.User) {
      params.user_message = {
        content: message.userContent,
      };
    } else {
      params.tool_messages = message.toolsData.map((data) => ({
        content: data.content,
        request_id: data.requestId,
      }));
    }
    this._messageReceiver!.send(params);
  }

  private _createMessageReceiver() {
    // 销毁旧的 MessageReceiver
    if (this._messageReceiver) {
      this._messageReceiver.dispose();
      this._messageReceiver = null;
    }
    this._messageReceiver = new SSEReceiver(this._conversationService);
  }

  private _updateMessage(message: AskMessage) {
    if (message.role === AskMessageRole.User) {
      const lastMessage = this._conversationService.getMessages().at(-1);
      const nextVersion = lastMessage
        ? typeof lastMessage.version === 'number'
          ? lastMessage.version + 1
          : Number(lastMessage.version) + 1
        : 0;
      const userMessage = new ClientMultiPartMessage({
        content: message.userContent,
        role: Role.User,
        createdAt: new Date().toISOString(),
        version: nextVersion,
      });

      this._conversationService.appendMessages([userMessage]);
      this._messageReceiver?.resetActiveStreamingMessage();
    }
  }

  private async _handleToolMessages(messages: ClientToolMessage[]) {
    const clientToolRunners: Promise<{ content: string; requestId: string }>[] = [];

    for (const toolMessage of messages) {
      const tool = this._getTools().find((tool) => tool.getInfo().name === toolMessage.name);
      if (!tool) continue;

      const runner = async () => {
        const result = await tool.run(
          toolMessage.input ?? '',
          toolMessage.id,
          this._conversationService.id,
          toolMessage.version,
        );
        return {
          content: result.value,
          requestId: toolMessage.id,
        };
      };

      clientToolRunners.push(runner());
    }

    if (clientToolRunners.length === 0) return;

    const toolsData = await Promise.all(clientToolRunners);
    console.log('ChatService.handleToolMessage0', toolsData);

    this.sendMessage({
      role: AskMessageRole.Tool,
      toolsData,
    });
  }

  private _getTools(): Tool[] {
    return [
      new ExecuteCommandTool(),
      new GetErrorsTool(),
      new GetAllErrorsTool(),
      this._instantiationService.createInstance(ListDirectoryTool),
      this._instantiationService.createInstance(ReadFileTool),
      this._instantiationService.createInstance(WriteFileTool),
      this._instantiationService.createInstance(ReplaceFileTool),
      this._instantiationService.createInstance(FetchRuleTool),
      this._instantiationService.createInstance(FigmaComponentDetailTool),
      ...this._mcpHubService.formatToToolInfos(),
    ];
  }
}
