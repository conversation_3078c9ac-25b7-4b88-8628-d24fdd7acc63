# Codin Contributing Guide

## Develop

### 准备工作

在Trae插件市场安装 esbuild Problem Matchers

```bash
nvm use 20 # pnpm v9

cd agent-extension

pnpm install # 安装依赖
```

### 开启调试
1. 打开 VsCode 左侧 Debug 面板
2. 点击运行 `Run Agent Extension`

### 设置ppe

编辑 agent-extension/.env文件，设置 NETWORK_PPE_ENV。例如

```
NETWORK_PPE_ENV=ppe_coding_01
```

### 排查问题

Trae顶部菜单->帮助->切换开发人员工具

即可看到日志。

### Merkle Tree Client 调试

1. 按上面的准备工作进行开发环境配置
2. 在 IDE 打开 agent-extension/src/extension.ts，解除有 // uncomment 标识的 Merkle Tree 启动流程
3. 在 agent-extension/src/extension.ts 文件打开状态下，按F5，会打开一个新的 IDE 窗口，这个窗口下的 IDE 将加载插件
4. 在新打开的 IDE 窗口下
  1. 打开 lvweb 项目
  2. 打开「输出」面板，筛选「Codin: Merkle Tree」的通道可以查看日志
  3. 或者在系统菜单->帮助->切换开发人员工具中的 Console 中也可以看到 [merkle-service] 开头的日志
5. IDE 打开、分支切换的时机会主动触发一次索引更新，并监听文件变更记录到本地 map 中，后续每五分钟与远端同步一次
