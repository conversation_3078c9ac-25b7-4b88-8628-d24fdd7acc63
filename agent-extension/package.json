{"name": "codin", "displayName": "剪映Codin", "publisher": "byted-extensions", "icon": "assets/codin-market-icon.png", "description": "Codin 是一款为剪映团队打造的 IDE 插件，提供智能的代码辅助和集成工具，让您的开发工作流程更快捷、更高效。", "repository": "******************:ies/codin.git", "version": "0.2.53", "engines": {"vscode": "^1.75.0"}, "scripts": {"vscode:prepublish": "npm run build", "compile": "node ./esbuild.js", "build": "NODE_ENV=production node ./esbuild.js", "package": "vsce package --no-dependencies", "watch": "node ./esbuild.js --watch", "test": "vitest run --coverage", "lint": "biome check", "typecheck": "tsc --noEmit"}, "main": "./out/extension.js", "extensionDependencies": ["vscode.git"], "contributes": {"viewsContainers": {"activitybar": [{"id": "codin", "title": "Codin", "icon": "assets/codin-icon.svg"}]}, "configuration": {"title": "Local Server", "properties": {"localServer.port": {"type": "number", "default": 40001, "description": "Port number for the local server"}}}, "commands": [{"command": "codin-agent.newConversation", "title": "New Conversation", "category": "codin-agent", "icon": "$(add)"}, {"command": "codin-agent.viewHistory", "title": "View History", "category": "codin-agent", "icon": "$(history)"}, {"command": "codin-agent.openInEditor", "title": "Open in Editor", "category": "codin-agent", "icon": "$(link-external)"}, {"command": "coding-agent.indexRecord", "title": "Index Record", "category": "codin-agent", "icon": "${history}"}, {"command": "codin.webview.codeReview", "title": "Code Review", "category": "codin-agent", "icon": "$(comment)"}, {"command": "codin.webview.oncall", "title": "Oncall", "category": "codin-agent"}, {"command": "codin.webview.account.login", "title": "<PERSON><PERSON>", "category": "codin-agent"}, {"command": "codin.webview.settings.open", "title": "Settings", "category": "codin-agent"}, {"command": "codin.webview.log.openLogFolder", "title": "Open Log Folder", "category": "codin-agent"}, {"command": "codin.webview.codediff.acceptAll", "title": "Accept all change", "category": "codin-agent"}, {"command": "codin.webview.codediff.revertAll", "title": "Revert all change", "category": "codin-agent"}], "menus": {"view/title": [{"command": "codin-agent.newConversation", "when": "view == codin.chatView", "group": "navigation@1"}, {"command": "codin-agent.viewHistory", "when": "view == codin.chatView", "group": "navigation@2"}, {"command": "codin-agent.openInEditor", "when": "view == codin.chatView", "group": "navigation@3"}, {"submenu": "codin.submenu", "when": "view == codin.chatView", "group": "navigation@4"}], "editor/title": [{"command": "codin-agent.newConversation", "when": "activeWebviewPanelId == codin.TabPanelProvider", "group": "navigation@1"}, {"command": "codin-agent.viewHistory", "when": "activeWebviewPanelId == codin.TabPanelProvider", "group": "navigation@2"}, {"command": "codin-agent.openInEditor", "when": "activeWebviewPanelId == codin.TabPanelProvider", "group": "navigation@3"}, {"submenu": "codin.submenu", "when": "activeWebviewPanelId == codin.TabPanelProvider", "group": "navigation@4"}], "codin.submenu": [{"command": "coding-agent.indexRecord", "title": "IndexRecord"}, {"command": "codin.webview.oncall", "title": "Oncall"}, {"command": "codin.webview.settings.open", "title": "Settings"}, {"command": "codin.webview.log.openLogFolder", "title": "Open Log Folder"}], "commandPalette": [{"command": "codin-agent.newConversation", "title": "Codin Agent: New Conversation"}, {"command": "codin-agent.viewHistory", "title": "Codin Agent: View History"}]}, "submenus": [{"id": "codin.submenu", "label": "More", "icon": "$(more)"}], "views": {"codin": [{"type": "webview", "icon": "assets/codin-icon.svg", "id": "codin.chatView", "name": "Codin"}]}}, "activationEvents": ["onStartupFinished"], "devDependencies": {"@biomejs/biome": "^1.9.4", "@byted-arch-fe/bam-code-generator": "^1.18.0", "@edenx/app-tools": "1.67.1", "@tailwindcss/typography": "^0.5.15", "@types/diff": "^5.2.1", "@types/glob": "^8.0.0", "@types/micromatch": "^4.0.9", "@types/node": "^22.8.6", "@types/react": "^18.2.65", "@types/react-dom": "^18.2.22", "@types/react-syntax-highlighter": "^15.5.13", "@types/vscode": "^1.74.0", "@types/vscode-webview": "^1.57.0", "@types/ws": "8.18.1", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitest/coverage-v8": "^3.2.4", "@volcengine/tos-sdk": "^2.7.5", "@vscode/vsce": "^3.4.2", "autoprefixer": "^10.4.18", "dotenv": "^16.5.0", "esbuild": "^0.16.10", "esbuild-plugin-copy": "^2.0.1", "esbuild-postcss": "^0.0.4", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "fzf": "^0.5.2", "glob": "^8.0.3", "ignore": "^7.0.3", "jsdom": "^26.1.0", "memfs": "^4.17.2", "postcss": "^8.4.35", "prettier": "^2.8.1", "tailwindcss": "^3.4.1", "typescript": "^5.6.3", "uuid": "^11.1.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4", "vitest-mock-extended": "^3.1.0", "yaml": "^2.8.0"}, "dependencies": {"@byted-image/codin-indexer": "0.2.25", "@byted-image/lv-bedrock": "1.5.4", "@byted-image/merkle": "^1.0.12", "@byted/frontier-web-sdk": "^1.25.9", "@byted/hooks": "^2.61.4", "@bytesso/device": "0.0.5", "@modelcontextprotocol/sdk": "^1.11.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@radix-ui/react-visually-hidden": "^1.1.0", "@radix-ui/themes": "^3.2.1", "@sasza/react-panzoom": "^1.19.0", "@slardar/base": "^2.1.2", "@slardar/integrations": "^2.13.0", "@slardar/web": "^1.14.5", "@swc/helpers": "^0.5.17", "@testing-library/react": "^16.3.0", "@tiptap/core": "^2.12.0", "@tiptap/extension-character-count": "^2.12.0", "@tiptap/extension-document": "^2.12.0", "@tiptap/extension-hard-break": "^2.12.0", "@tiptap/extension-history": "^2.12.0", "@tiptap/extension-mention": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-text": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "@vscode/webview-ui-toolkit": "^1.2.2", "axios": "^1.5.1", "chokidar": "^4.0.1", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "diff": "^5.2.0", "fast-deep-equal": "^3.1.3", "framer-motion": "^11.3.19", "globby": "^14.1.0", "iconv-lite": "^0.6.3", "ignore": "^7.0.5", "isbinaryfile": "^5.0.2", "js-md5": "^0.8.3", "jschardet": "^3.1.4", "lodash": "^4.17.21", "lucide-react": "^0.446.0", "mermaid": "^11.6.0", "micromatch": "^4.0.8", "nanoid": "^5.0.8", "node-fetch-cache": "^5.0.2", "path": "^0.12.7", "pino": "^9.7.0", "pino-roll": "^3.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-json-view": "^1.21.3", "react-markdown": "^9.0.1", "react-modal": "^3.16.3", "react-resizable-panels": "^2.1.7", "react-router-dom": "^6.22.3", "react-syntax-highlighter": "^15.6.1", "reflect-metadata": "0.1.13", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.0", "sonner": "^1.5.0", "swr": "^2.2.5", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "tippy.js": "^6.3.7", "usehooks-ts": "^3.1.0", "ws": "8.18.1", "xxhash-wasm": "^1.1.0", "zod": "^3.23.8"}}